"""${message}

Revision ID: ${up_revision}
Revises: ${down_revision | comma,n}
Create Date: ${create_date}

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
${imports if imports else ""}

# revision identifiers, used by Alembic.
revision: str = ${repr(up_revision)}
down_revision: Union[str, None] = ${repr(down_revision)}
branch_labels: Union[str, Sequence[str], None] = ${repr(branch_labels)}
depends_on: Union[str, Sequence[str], None] = ${repr(depends_on)}


def upgrade() -> None:
    op.execute('SET foreign_key_checks = 0;')

    ${upgrades if upgrades else "pass"}

    op.execute('SET foreign_key_checks = 1;')


def downgrade() -> None:
    op.execute('SET foreign_key_checks = 0;')

    ${downgrades if downgrades else "pass"}

    op.execute('SET foreign_key_checks = 1;')
