import time
import uuid
from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import ORJSONResponse
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger


from app.views import register_routes
from app.config import settings


fast_api_app = FastAPI(default_response_class=ORJSONResponse, **settings.FASTAPI_INIT_OPTIONS)


from commons.decorators import custom_exception_handler, custom_request_logger, custom_request_validation

golbal_exception_handlers = {
    RequestValidationError: custom_request_validation,
    HTTPException: custom_exception_handler,
    Exception: custom_exception_handler,
}

fast_api_app.add_middleware(BaseHTTPMiddleware, dispatch=custom_request_logger)


fast_api_app.add_middleware(
    CORSMiddleware,
    # 这里配置允许跨域访问的前端地址
    allow_origins=settings.FASTAPI_CORS_ORIGINS,
    # 跨域请求是否支持 cookie， 如果这里配置true，则allow_origins不能配置*
    allow_credentials=True,
    # 支持跨域的请求类型，可以单独配置get、post等，也可以直接使用通配符*表示支持所有
    allow_methods=["*"],
    allow_headers=["*"],
)

register_routes(fast_api_app)

__ALL__ = ['fast_api_app']
