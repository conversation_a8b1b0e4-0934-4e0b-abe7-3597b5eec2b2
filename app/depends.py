'''
这里存放fastapi的depends
'''

import os
from fastapi.security import OAuth2PasswordBearer


from app.services.exchange_rate_service import ChinamoneyExchangeRateServices, UnionpayintlExchangeRateServices
from commons.extensions.redis_extras import RedisPool
from commons.depends import get_current_user
from fastapi import Depends
from typing_extensions import Annotated

# from app.extensions.db_extras import AsyncDatabase
from app.config import settings
from commons.sdks.base import SdkClient
from commons.sdks.flight_fare.current_admin import GetCurrentAdminRequest

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=settings.LOGIN_URL)


redis_pool = RedisPool(redis_url=settings.REDIS_URL)

ex_rate_service = UnionpayintlExchangeRateServices(json_file=os.path.join(settings.STATIC_PATH, 'exchange_rate.json'))
chinamoney_ex_rate_service = ChinamoneyExchangeRateServices(
    json_file=os.path.join(settings.STATIC_PATH, 'chinamoney_exchange_rate.json')
)


async def get_current_admin_base(token: Annotated[str, Depends(oauth2_scheme)]):
    """在不需要校验权限的地方，只解析token即可"""
    return await get_current_user(token=token, secret_key=settings.SECRET_KEY, algorithm=settings.ALGORITHM)


async def get_current_admin_full(token: Annotated[str, Depends(oauth2_scheme)]):
    """在需要校验权限的地方，需要从运价模块获取完整的用户信息"""
    request = GetCurrentAdminRequest()
    request._headers = {'Authorization': f'Bearer {token}'}
    flight_fare_sdk = SdkClient(host=settings.FLIGHT_FARE_URL)
    admin_row = await flight_fare_sdk.send_async(request)
    return admin_row
