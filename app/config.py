import os
import sys
from typing import Any, Dict, List, Optional
from loguru import logger
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field

from commons.cores.config_manager import ConfigManager
from commons.extensions.logger_extras import DEFAULT_LOG_FORMAT, log_uid


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file='.env', env_file_encoding='utf-8')

    ROOT_PATH: str = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
    STATIC_PATH: str = os.path.join(ROOT_PATH, 'statics')
    DEBUG: bool = Field(False, description="是否开启debug模式")
    #
    LOG_PATH: str = Field(os.path.join(ROOT_PATH, 'logs'), description="日志路径")
    LOG_LEVEL: str = Field('INFO', description="日志等级")
    LOG_FORMAT: str = Field(DEFAULT_LOG_FORMAT, description="日志格式")
    LOG_BACKTRACE: bool = Field(False, description="是否开启日志回溯")
    LOG_DIAGNOSE: bool = Field(False, description="是否开启日志诊断")
    ES_LOG_TAG: str = Field('elasticsearch', description="ES日志标签")
    ES_LOG_LEVEL: str = Field('INFO', description="ES日志等级")
    ES_LOG_ROTATION: str = Field('500 MB', description="ES日志文件大小")
    # ========= MYSQL ==========
    # 异步操作数据库
    SQLALCHEMY_DATABASE_URI: str = Field(
        'mysql+aiomysql://root:password@localhost/basename', description="异步数据库连接url"
    )
    SQLALCHEMY_ECHO: bool = Field(False, description="是否开启sqlalchemy echo")
    # 每n秒检查一次连接池（重要，可避免链接超时断开）
    SQLALCHEMY_POOL_RECYCLE: int = Field(7200, description="每n秒检查一次连接池（重要，可避免链接超时断开）")
    # 连接池最大连接数
    SQLALCHEMY_POOL_SIZE: int = Field(50, description="连接池最大连接数")
    # 连接池最大等待时间
    SQLALCHEMY_POOL_TIMEOUT: int = Field(30, description="连接池最大等待时间")
    # 连接池超出最大连接数时，最大超出上限
    SQLALCHEMY_MAX_OVERFLOW: int = Field(10, description="连接池超出最大连接数时，最大超出上限")
    # ========= 业务配置 ==========

    # ========= REDIS ==========
    REDIS_URL: str = Field(
        'redis://localhost:6379/1?health_check_interval=60&decode_responses=True', description="redis连接url"
    )
    # ========= FastAPI ==========
    FASTAPI_INIT_OPTIONS: Optional[Dict] = Field(
        {'docs_url': '', 'redoc_url': '', 'openapi_url': '', 'swagger_ui_oauth2_redirect_url': ''},
        description="fastapi配置",
    )

    FASTAPI_CORS_ORIGINS: Optional[List[str]] = Field(
        ['http://localhost:9528', 'http://127.0.0.1:9528'], description="跨域白名单"
    )

    API_PERFIX: str = Field('/api/v1/pay_center', description="api前缀")
    # openssl rand -hex 32
    SECRET_KEY: str = Field(..., description="密钥")  # = env.str('SECRET_KEY')
    ALGORITHM: str = Field('HS256', description="加密算法")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(60 * 24 * 7, description="token过期时间")  # 7 天

    # ========= 业务配置 ==========

    FLIGHT_FARE_URL: str = Field('http://*************:9528', description="运价模块API地址")
    FLIGHT_ORDER_URL: str = Field('http://127.0.0.1:8081', description="订单模块API地址")
    FLIGHT_PRE_ORDER_URL: str = Field('http://*************:8082', description="压位模块API地址")
    CRAWLER_URL: str = Field('http://*************:9529', description="压位模块API地址")

    PUBLIC_PAY_START_CHECK_URL: str = Field(
        f'http://*************:8080/api/v1/flight_order/public/order/pay/start', description="支付启动前检查"
    )

    LOGIN_URL: str = Field(f'/api/v1/flight_fare/admin/login/token', description="登录url,注意前缀配置成运价系统地址")
    BOOK_QUEUES: Dict[str, str] = Field({"fare_type": "ze_book_queue"}, description="预定任务队列")

    TRIPLINKINTL_HOST: str = Field("https://compass.triplinkintl.com/compass/api", description="国际航班链接AES密钥")
    TRIPLINKINTL_CUSTOMER_ID: str = Field(description="客户端ID")
    TRIPLINKINTL_AES_KEY: str = Field(description="客户端AES密钥，用于加密请求体")
    TRIPLINKINTL_T_RSA_PUBLIC_KEY: str = Field(description="triplinkintl提供的公钥，用于验证结果签名")
    TRIPLINKINTL_U_RSA_PRIVATE_KEY: str = Field(description="客户端私钥，用于签名请求数据")
    TRIPLINKINTL_SUPPORTED_MCC_GROUP: str = Field('Flig', description="支持的MCC组")
    TRIPLINKINTL_CARD_MAP: Dict[str, Any] = Field(
        {"credit_card_hk": {"products": ["C09"], "types": ["GWTTP"], "labels": ["MasterCard"]}},
        description="虚拟卡产品编号对应",
    )
    TRIPLINKINTL_CARD_BIN: List[str] = Field(default_factory=list, description="支持的卡BIN")
    TRIPLINKINTL_SETTLEMENT_CURRENCY_CODE: str = Field('USD', description="支持的卡BIN")
    # 531797 对应类型 MCO
    # 54346410、522981、539593 对应类型 GWTTP
    # TRIPLINKINTL_CARD_BIN: List[str] = Field(['531797', '54346410'], description="支持的卡BIN")

    AIRLINE_CARD_MAP: Dict[str, List[str]] = Field(
        {'ZE': ['credit_card_hk'], 'VZ': ['credit_card_hk'], 'VJ': ['credit_card_hk'], '5J': ['credit_card_hk']},
        description="航司对应虚拟卡产品编号",
    )
    CARD_LIMIT_CONFIG: Dict[str, Any] = Field(
        {
            'ZE': {'currency': 'KRW', 'triplink_settlement_currency_code': 'USD', 'cny_limit': 8000, 'limit_times': 1},
            'VZ': {'currency': 'KRW', 'triplink_settlement_currency_code': 'USD', 'cny_limit': 8000, 'limit_times': 1},
            'VJ': {'currency': 'KRW', 'triplink_settlement_currency_code': 'USD', 'cny_limit': 8000, 'limit_times': 1},
            '5J': {'currency': 'KRW', 'triplink_settlement_currency_code': 'USD', 'cny_limit': 8000, 'limit_times': 1},
        },
        description="虚拟卡配置",
    )
    CARD_LIMIT_OFFSET: int = Field(5, description="虚拟卡限额偏移量，不宜太大，因为结算币种可能是非人民币")

    AIRWALLEX_HOST: str = Field('https://api.airwallex.com', description="AirWallex API地址")
    AIRWALLEX_ACCOUNT: str = Field(..., description="AirWallex 账户")
    AIRWALLEX_ACCOUNT_ID: str = Field(..., description="AirWallex 账户ID")
    AIRWALLEX_CLIENT_ID: str = Field(..., description="AirWallex client ID")
    AIRWALLEX_API_KEY: str = Field(..., description="AirWallex API Key")

    SUPPLIERS: List[str] = Field(default_factory=list, description="供应商")


settings = Settings()

new_settings = ConfigManager(settings_files=[os.path.join(settings.ROOT_PATH, 'configs', 'default.toml')])

# ========== 日志配置 ==========
logger.remove()
logger.add(
    sys.stderr,
    level=settings.LOG_LEVEL,
    format=settings.LOG_FORMAT,
    backtrace=settings.LOG_BACKTRACE,
    diagnose=settings.LOG_DIAGNOSE,
    filter=lambda record: record["extra"].get("write_tag") is None,
)


logger.add(
    os.path.join(settings.LOG_PATH, 'task.log'),
    level=settings.ES_LOG_LEVEL,
    rotation=settings.ES_LOG_ROTATION,
    # serialize=True,
    format='{message}',
    filter=lambda record: record["extra"].get("write_tag") == settings.ES_LOG_TAG,
)
logger.opt(exception=True)
logger.configure(extra={"unique_id": log_uid})

logger.debug('config loaded')
