import asyncio
from contextlib import asynccontextmanager
import glob
import importlib
import os
import re


from typing import AsyncGenerator


from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_scoped_session, async_sessionmaker


from app.config import settings


async_engine = create_async_engine(
    settings.SQLALCHEMY_DATABASE_URI,
    echo=settings.SQLALCHEMY_ECHO,
    pool_recycle=settings.SQLALCHEMY_POOL_RECYCLE,
    pool_size=settings.SQLALCHEMY_POOL_SIZE,
    pool_timeout=settings.SQLALCHEMY_POOL_TIMEOUT,
    max_overflow=settings.SQLALCHEMY_MAX_OVERFLOW,
)

AsyncSessionLocal = async_scoped_session(
    async_sessionmaker(async_engine, expire_on_commit=False, class_=AsyncSession), scopefunc=asyncio.current_task
)


def import_all_models():
    model_files = glob.glob(os.path.join(settings.ROOT_PATH, 'app', 'models', '*.py'))
    if not model_files:
        model_files = glob.glob(os.path.join(settings.ROOT_PATH, 'app', 'models', '*.pyc'))

    for file in model_files:
        # 将路径转换为模块名
        module_name = file[:-3].replace(settings.ROOT_PATH, '')
        module_name = re.sub(r'[\\\/]', '.', module_name[1:])
        # 排除 __init__ 和 base 模块
        if module_name.split('.')[-1] in ('__init__', 'base'):
            continue
        mod = importlib.import_module(module_name)


@asynccontextmanager
async def get_db_session_async() -> AsyncGenerator:
    session = None
    try:
        session = AsyncSessionLocal()
        yield session
        await session.commit()
    except Exception as e:
        if session:
            await session.rollback()
        raise e
    finally:
        if session:
            await session.close()
