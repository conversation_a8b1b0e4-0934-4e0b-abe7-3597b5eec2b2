from sqlalchemy import DECIMAL, TEXT, Column, Date, Integer, String, DateTime
from app.consts.status import CardStatus
from app.consts.types import ApplyChannel
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions


class PayCard(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "pay_cards"
    __table_args__ = {"comment": "支付卡表"}

    order_no = Column(String(64), nullable=False, index=True, server_default='', comment="订单号")
    supplier = Column(String(32), nullable=False, server_default='', comment="虚拟卡供应商")
    # 卡信息
    supplier_card_id = Column(String(128), nullable=False, index=True, server_default='', comment="供应商侧的card_id")
    card_no = Column(String(32), nullable=False, index=True, server_default='', comment="卡号")
    card_expiration_date = Column(String(8), nullable=False, server_default='', comment="卡有效期")
    cvv2 = Column(String(8), nullable=False, server_default='', comment="卡CVV2")
    card_type = Column(String(32), nullable=False, server_default='', comment="卡类型/品牌")
    card_label = Column(String(32), nullable=False, server_default='', comment="卡组织")
    # 授权和限制信息
    currency_code = Column(String(8), nullable=False, server_default='', comment="币种")
    card_limit = Column(DECIMAL(20, 2), nullable=False, server_default='0.00', comment="卡额度")
    min_auth_amount = Column(DECIMAL(20, 2), nullable=False, server_default='0.00', comment="最低授权额度")
    max_auth_amount = Column(DECIMAL(20, 2), nullable=False, server_default='0.00', comment="最高授权额度")
    max_auth_times = Column(Integer, nullable=False, server_default='1', comment="最大授权次数")
    card_close_usage = Column(
        Integer,
        nullable=False,
        server_default="100",
        comment="自动注销比例：1-100，已清算金额大于等于对应比例时自动注销，0表示不注销",
    )
    # 卡状态
    status = Column(String(32), nullable=False, server_default=CardStatus.PENDING.value, comment="卡状态")
    # 创建信息
    create_request_snapshot = Column(TEXT, nullable=False, comment="创建请求快照")
    create_response_snapshot = Column(TEXT, nullable=False, comment="创建响应快照")
    # 使用信息
    actual_paid_amount = Column(DECIMAL(20, 2), nullable=False, server_default='0.00', comment="实际支付金额")
    flight_dep_time = Column(DateTime, nullable=False, comment="航班起飞时间")

    admin_id = Column(Integer, nullable=False, index=True, server_default='0', comment="账号ID")
    username = Column(String(32), nullable=False, server_default="", comment="用户名")
    remark = Column(String(255), nullable=False, server_default="", comment="备注")
