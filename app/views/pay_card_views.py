from datetime import datetime
from typing_extensions import Annotated
from fastapi import APIRouter, Depends, HTTPException, Request
from loguru import logger
import orjson
from pydantic import BaseModel

from app.config import settings

from app.depends import get_current_admin_base, oauth2_scheme


from app.models.pay_card import PayCard
from app.services import pay_card_service
from app.views.schemas import pay_card_schemas
from commons.consts.api_codes import ApiCodes
from commons.depends import get_real_client_ip
from commons.sdks.base import SdkClient
from commons import sdks as hy_sdks

routers = APIRouter(prefix=f'{settings.API_PERFIX}/pay_card', tags=['支付卡管理接口'])


@routers.post("/search", summary="支付卡管理", response_model=pay_card_schemas.SearchOut)
async def search(
    item: pay_card_schemas.SearchIn,
    current_admin: dict = Depends(get_current_admin_base),
    client_ip: str = Depends(get_real_client_ip),
):
    conditions = []

    if item.conditions.admin_id:
        conditions.append(PayCard.admin_id == item.conditions.admin_id)
    else:
        conditions.append(PayCard.admin_id > 0)

    if item.conditions.order_no:
        conditions.append(PayCard.order_no == item.conditions.order_no)

    # if item.conditions.airline_code:
    #     conditions.append(PreOrderTask.airline_code == item.conditions.airline_code)
    # if item.conditions.flight_no:
    #     conditions.append(PreOrderTask.flight_nos == item.conditions.flight_no)

    total = await PayCard.count_async(*conditions)
    page = item.page if item.page else 1
    page_size = item.page_size if item.page_size else 10
    offset = (page - 1) * page_size
    results = await PayCard.get_all_async(*conditions, limit=page_size, offset=offset, order_by=[PayCard.id.desc()])
    logger.debug(results)

    return ApiCodes.SUCCESS.generate_api_result(data={'total': total, 'rows': results})


@routers.post("/create", summary="支付卡创建", response_model=pay_card_schemas.DecryptedOut)
async def create(
    item: pay_card_schemas.CreateIn,
    token: Annotated[str, Depends(oauth2_scheme)],
    request: Request,
    current_admin: dict = Depends(get_current_admin_base),
    client_ip: str = Depends(get_real_client_ip),
):
    fare_sdk = hy_sdks.base.SdkClient(host=settings.FLIGHT_FARE_URL)
    auth_request = hy_sdks.flight_fare.auth_by_password.AuthByPasswordRequest(password=item.confirm_password)
    auth_request._headers['Authorization'] = f'Bearer {token}'
    auth_request._headers['X-Request-ID'] = request.state.request_id
    auth_result = await fare_sdk.send_async(auth_request)
    logger.debug(auth_result)
    if not auth_result.get('data'):
        raise HTTPException(status_code=400, detail=auth_result.get('message', '验证密码失败'))

    pay_card_row = await pay_card_service.grenade_card(
        order_no=item.order_no,
        airline_code='ZE',
        flight_dep_time=item.flight_dep_time,
        currency_code=item.currency_code,
        supplier_code=item.supplier_code,
        card_limit=item.card_limit,
        admin_id=current_admin['id'],
        username=current_admin['username_desc'],
    )
    return ApiCodes.SUCCESS.generate_api_result(data=pay_card_row)


@routers.post("/detail/decrypted", summary="支付卡详情(展示cvv)", response_model=pay_card_schemas.DecryptedOut)
async def get_decrypted_detail(
    item: pay_card_schemas.DecryptedIn,
    token: Annotated[str, Depends(oauth2_scheme)],
    request: Request,
    current_admin: dict = Depends(get_current_admin_base),
    client_ip: str = Depends(get_real_client_ip),
):
    fare_sdk = hy_sdks.base.SdkClient(host=settings.FLIGHT_FARE_URL)
    auth_request = hy_sdks.flight_fare.auth_by_password.AuthByPasswordRequest(password=item.confirm_password)
    auth_request._headers['Authorization'] = f'Bearer {token}'
    auth_request._headers['X-Request-ID'] = request.state.request_id
    auth_result = await fare_sdk.send_async(auth_request)
    if not auth_result.get('data'):
        raise HTTPException(status_code=400, detail=auth_result.get('message', '验证密码失败'))

    pay_card_row = await PayCard.get_by_async(PayCard.id == item.id)
    if current_admin['id'] != pay_card_row['admin_id']:
        raise HTTPException(status_code=400, detail=f"仅限卡片申请人查看")
    return ApiCodes.SUCCESS.generate_api_result(data=pay_card_row)
