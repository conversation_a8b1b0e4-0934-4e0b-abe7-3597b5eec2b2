from datetime import date, datetime
from typing import Any, List, Optional, Dict, Union
from loguru import logger
from pydantic import BaseModel, Field, model_validator


from app.consts.supplier_codes import SupplierCodes
from app.views.schemas.base_schemas import ModelMixin
from app.views.schemas.model_schemas import PayCardBase
from commons.fastapi.schemas.common_schemas import BaseApiOut


class PayTaskCreateIn(BaseModel, ModelMixin):
    order_no: str = Field(..., description="订单号")
    third_no: str = Field(None, description="第三方订单号")
    airline_code: str = Field(..., description="航空公司代码")
    pay_url: str = Field(..., description="支付链接")
    pay_amount: float = Field(..., description="支付金额")
    currency_code: str = Field(..., description="币种")
    pay_limit_time: str = Field(None, description="支付截止(释放)时间")


class ExchangeRateIn(BaseModel, ModelMixin):
    from_currency: str = Field(..., description="原币种")
    to_currency: str = Field(..., description="目标币种")
    amount: float = Field(None, description="原币金额")


class ExchangeRate(ExchangeRateIn):
    rate: float = Field(..., description="汇率")


class ExchangeRateOut(BaseApiOut):
    data: ExchangeRate
