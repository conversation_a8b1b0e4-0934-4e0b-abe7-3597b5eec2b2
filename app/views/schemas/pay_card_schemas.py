from datetime import date, datetime
from typing import Any, List, Optional, Dict, Union
from loguru import logger
from pydantic import BaseModel, Field, field_validator, model_validator


from app.consts.supplier_codes import SupplierCodes
from app.views.schemas.base_schemas import ModelMixin
from app.views.schemas.model_schemas import PayCardBase, PayCardFull
from commons.fastapi.schemas.common_schemas import BaseApiOut, BaseSearchIn, BaseSearchResult


class CreateIn(BaseModel, ModelMixin):
    order_no: str = Field(None, description="订单号")
    flight_dep_time: str = Field(..., description="航班出发时间 YYYY-MM-DD HH:mm:ss")
    currency_code: str = Field(..., description="币种")
    supplier_code: str = Field(..., description="供应商代码")
    card_limit: float = Field(..., description="银行卡额度")
    confirm_password: str = Field(..., description="确认密码")
    remark: Optional[str] = Field(None, description="备注")


class CardInfoEncrypted(PayCardFull):
    @field_validator('card_no', 'card_expiration_date', 'cvv2', mode='before')
    @classmethod
    def encrypt(cls, v: Any):
        if len(v) > 10:
            prefix = v[:6]
            suffix = v[-4:]
            return prefix + '*' * (len(v) - 10) + suffix
        if len(v) > 4:
            return '*' * len(v[:-4]) + v[-4:]
        return '*' * len(v)


class DecryptedOut(BaseApiOut):
    data: PayCardFull


class DecryptedIn(BaseModel):
    id: int = Field(..., description="主键ID")
    confirm_password: str = Field(..., description="确认密码")


class Conditions(BaseModel):
    admin_id: Optional[int] = Field(None, description="账号ID")
    order_no: Optional[str] = Field(None, description="订单号")


class SearchIn(BaseSearchIn):
    conditions: Optional[Conditions] = Field(None, description="查询条件")


class SearchResult(BaseSearchResult):
    rows: Optional[List[CardInfoEncrypted]] = Field(default_factory=list, description="银行卡信息")


class SearchOut(BaseApiOut):
    data: SearchResult
