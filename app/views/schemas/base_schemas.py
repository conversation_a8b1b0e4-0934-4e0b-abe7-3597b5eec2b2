from datetime import date, datetime
from typing import Any, List, Optional, Dict, Union
from loguru import logger
from pydantic import BaseModel, Field, model_validator


from app.consts.supplier_codes import SupplierCodes
from app.views.schemas.model_schemas import PayCardBase
from commons.fastapi.schemas.common_schemas import BaseApiOut


class ModelMixin:

    @model_validator(mode='before')
    @classmethod
    def parse_values(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        logger.debug(values)
        # 自动生成 requestId 和自动填充 customerId
        if 'airline_code' in values:
            values['airline_code'] = values['airline_code'].upper()
        if 'currency_code' in values:
            values['currency_code'] = values['currency_code'].upper()
        if 'from_currency' in values:
            values['from_currency'] = values['from_currency'].upper()
        if 'to_currency' in values:
            values['to_currency'] = values['to_currency'].upper()

        return values
