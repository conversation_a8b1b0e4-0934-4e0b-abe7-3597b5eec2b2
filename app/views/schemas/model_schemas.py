from datetime import date, datetime
from typing import List, Optional, Dict, Union
from pydantic import BaseModel, Field


class PayCardBase(BaseModel):
    order_no: str = Field(..., description="订单号")
    card_no: str = Field(..., description="卡号")
    card_expiration_date: str = Field(..., description="卡有效期")
    cvv2: str = Field(..., description="卡CVV2")
    card_type: str = Field(..., description="卡类型")
    card_label: str = Field(..., description="卡组织")


class PayCardFull(PayCardBase):
    id: int = Field(..., description="主键ID")

    card_limit: float = Field(..., description="银行卡额度")
    currency_code: str = Field(..., description="币种")
    status: str = Field(..., description="状态")
    admin_id: Optional[int] = Field(None, description="账号ID")
    username: Optional[str] = Field(None, description="用户名")
    remark: Optional[str] = Field(None, description="备注")
    created: str = Field(..., description="创建时间")
    updated: str = Field(..., description="更新时间")
