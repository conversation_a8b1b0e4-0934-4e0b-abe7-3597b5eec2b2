from datetime import datetime, timedelta
import os
import time
from fastapi import APIRouter, Depends, HTTPException
from loguru import logger
from pydantic import BaseModel

from app.config import settings

from app.extensions.redis_extras import RedisPool
from app.models.pay_card import PayCard
from app.sdks.flight_order_sdk import FlightOrderSdk
from app.services import pay_card_service
from app.views.schemas import public_schemas
from commons.consts.api_codes import ApiCodes
from commons.depends import get_real_client_ip
from commons.fastapi.schemas import common_schemas

# import commons.sdks.pay_center.generate_card
from commons.sdks import pay_center
from app.depends import ex_rate_service, chinamoney_ex_rate_service

routers = APIRouter(prefix=f'{settings.API_PERFIX}/public', tags=['对外开放的API接口'])


# 普通接口
@routers.post("/pay/task/create", summary="支付任务创建", response_model=common_schemas.BaseApiOut)
async def pay_task_create(item: public_schemas.PayTaskCreateIn, client_ip: str = Depends(get_real_client_ip)):

    return ApiCodes.SUCCESS.generate_api_result(data=True)


@routers.post("/pay/task/mock", summary="回调模拟", response_model=common_schemas.BaseApiOut)
async def pay_task_mock(item: public_schemas.PayTaskCreateIn, client_ip: str = Depends(get_real_client_ip)):
    order_sdk = FlightOrderSdk(host=settings.FLIGHT_ORDER_URL)

    data = {'order_no': item.order_no}
    resp = await order_sdk.pay_start(data=data)
    logger.debug(f'支付检查 {data} {resp}')
    if resp.get('code', -1) == ApiCodes.SUCCESS.value and resp.get('data', False) is True:
        # 7. 回调支付结果
        result = {
            "task_info": item.model_dump(),
            "data": {
                "order_no": item.order_no,
                "trade_no": str(int(time.time() * 1000)),
                "pay_account": "async678",
                "pay_amount": item.pay_amount,
                "currency_code": item.currency_code,
            },
            "error": {"code": ApiCodes.SUCCESS.value, "message": ApiCodes.SUCCESS.label},
        }
        logger.debug(f'模拟支付回调 {result}')
        await order_sdk.pay_callback(data=result)
    return ApiCodes.SUCCESS.generate_api_result(data=True)


@routers.post(
    "/pay_card/create", summary="获取虚拟支付卡", response_model=pay_center.public.PayCenterPublicCreateCardResponse
)
async def get_pay_card(
    item: pay_center.public.PayCenterPublicCreateCardRequest, client_ip: str = Depends(get_real_client_ip)
):
    flight_dep_time = datetime.strptime(item.flight_dep_time, '%Y-%m-%d %H:%M:%S')
    if flight_dep_time < datetime.now():
        return ApiCodes.PARAMS_ERROR.generate_api_result(ext_msg='航班出发时间不能小于当前时间')

    pay_card_row = await pay_card_service.grenade_card(
        order_no=item.order_no,
        airline_code=item.airline_code,
        flight_dep_time=item.flight_dep_time,
        card_limit=item.total_price,
        # card_limit=1,  # TODO TEST 测试
        currency_code=item.currency_code,
        supplier_code=item.supplier_code if item.supplier_code else 'triplinkintl',
        channel_type=item.channel_type,
    )
    return ApiCodes.SUCCESS.generate_api_result(data=pay_card_row)


@routers.post("/pay_card/list", summary="获取卡片列表", response_model=pay_center.get_card_list.GetCardListResponse)
async def get_pay_card_list(
    item: pay_center.get_card_list.GetCardListRequest, client_ip: str = Depends(get_real_client_ip)
):
    pay_card_rows = await pay_card_service.get_card_list(order_no=item.order_no)
    return ApiCodes.SUCCESS.generate_api_result(data=pay_card_rows)


@routers.post("/pay_card/secret", summary="获取卡片", response_model=pay_center.get_card_secret.GetCardSecretResponse)
async def exchange_rate(
    item: pay_center.get_card_secret.GetCardSecretRequest, client_ip: str = Depends(get_real_client_ip)
):
    row = await PayCard.get_by_async(PayCard.id == item.id)
    return ApiCodes.SUCCESS.generate_api_result(data=row)


@routers.post("/exchange/rate", summary="获取汇率", response_model=pay_center.exchange_rate.GetExchangeRateResponse)
async def exchange_rate(
    item: pay_center.exchange_rate.GetExchangeRateRequest, client_ip: str = Depends(get_real_client_ip)
):
    rate = None
    result = item.model_dump(exclude_none=True, exclude_unset=True)
    async with RedisPool() as redis:
        rate = await redis.get(f'exchange_rate_{item.from_currency}_{item.to_currency}')
        if rate and item.use_cache:
            result['rate'] = float(rate)
        else:
            if item.service_type == 'unionpayintl':
                await ex_rate_service.auto_refresh()
                rate = ex_rate_service.get_rate(from_code=item.from_currency, to_code=item.to_currency)
            elif item.service_type == 'chinamoney':
                await chinamoney_ex_rate_service.auto_refresh()
                rate = chinamoney_ex_rate_service.get_rate(from_code=item.from_currency, to_code=item.to_currency)
            result = item.model_dump(exclude_none=True, exclude_unset=True)

            if not rate:
                raise HTTPException(
                    status_code=404, detail=f'cannot find rate from {item.from_currency} to {item.to_currency}'
                )
            else:
                result['rate'] = rate['rateData']
                await redis.set(f'exchange_rate_{item.from_currency}_{item.to_currency}', result['rate'], ex=60 * 60)
    logger.debug(result)
    return ApiCodes.SUCCESS.generate_api_result(data=result)
