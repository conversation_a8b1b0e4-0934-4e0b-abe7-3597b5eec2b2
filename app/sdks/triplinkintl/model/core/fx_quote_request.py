from typing import Dict, Optional
from pydantic import Field

from app.sdks.triplinkintl.model.base_request import BaseRequest


class TriplinkintlFxQuoteRequest(BaseRequest):
    """汇率查询"""

    '''
    sellCurrency	String	Y	卖出币种	ISO 4217货币编号，3位数字。
    buyCurrency	String	Y	买入币种	ISO 4217货币编号，3位数字。
    fxDirection	Number	Y	交易方向	0：指定卖出，此时fxAmount为卖出金额；
    1：指定买入，此时fxAmount为买入金额。
    fxAmount	Number	Y	交易金额	小数，对应交易币种。
    '''
    sellCurrency: str = Field(..., description="卖出币种")
    buyCurrency: str = Field(..., description="买入币种")
    fxDirection: int = Field(0, description="交易方向")
    fxAmount: float = Field(1, description="交易金额")
