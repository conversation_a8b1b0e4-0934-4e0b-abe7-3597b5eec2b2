from typing import Dict, Optional
from pydantic import Field

from app.sdks.triplinkintl.model.base_request import BaseRequest


class TriplinkintlCreateCardRequest(BaseRequest):
    """创建卡"""

    cardCurrencyCode: Optional[str] = Field(None, description="卡币种，默认与结算币种相同，ISO 4217货币编号，3位数字")
    settlementCurrencyCode: str = Field(..., description="结算币种，ISO 4217货币编号，3位数字")
    exchangeCurrencyCode: Optional[str] = Field(
        None, description="卖出币种，ISO 4217货币编号，3位数字，卡产品为C05时生效且必填"
    )
    activeDate: str = Field(..., description="生效日期，格式 yyyy-MM-dd")
    inactiveDate: str = Field(..., description="失效日期，格式 yyyy-MM-dd")
    cardLimit: float = Field(..., description="额度，小数，对应卡币种")
    minAuthAmount: float = Field(..., description="单次授权金额下限，小数，对应卡币种，预付费卡此字段不生效")
    maxAuthAmount: float = Field(..., description="单次授权金额上限，小数，对应卡币种，预付费卡此字段不生效")
    maxAuthTimes: int = Field(1, description="授权次数上限，整数。1：单次卡，-1：不限次数")
    cardCloseUsage: Optional[int] = Field(100, description="自动注销百分比，整数，范围0-100，默认0")
    supportedMccGroup: str = Field(..., description="接受的MCC组，接受的商户类别组名，由TripLink定义")
    supportedMid: Optional[str] = Field(None, description="接受的商户ID，非此商户请求的交易会被拒绝")
    supportedAcquirerId: Optional[str] = Field(None, description="接受的收单行ID，非此收单行请求的交易会被拒绝")
    multipleCurrencyCard: Optional[bool] = Field(True, description="是否允许非卡币种交易，默认true")
    cvv2ForceCheck: Optional[bool] = Field(False, description="是否验证CVV2，默认false")
    allow3ds: Optional[bool] = Field(True, description="是否接受3DS交易，默认true，仅香港万事达卡生效")
    cardProductCode: str = Field(..., description="卡产品，枚举值，详见卡分类层次下拉框")
    cardType: str = Field(..., description="卡类型，枚举值，详见卡分类层次下拉框")
    cardLabel: str = Field(..., description="卡组织，枚举值，详见卡分类层次下拉框")
    cardBin: Optional[str] = Field(None, description="发卡机构识别号，可指定开卡卡BIN段，请联系TripLink提供")
    quoteId: Optional[str] = Field(None, description="汇率ID，查询汇率返回的汇率ID")
    timeZone: Optional[str] = Field(
        None, description="卡时区，格式举例：中国标准时间 GMT+08:00，北美东部标准时间 GMT-05:00"
    )
    userReferenceMap: Optional[Dict[str, str]] = Field(None, description="用户自定义字段，String键值对，共20个键可选")
