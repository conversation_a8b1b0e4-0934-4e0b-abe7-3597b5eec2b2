from datetime import date
from typing import Any, Dict, Optional
import uuid
from pydantic import BaseModel, Field, model_validator


class BaseRequest(BaseModel):
    requestId: str = Field(..., description="请求流水号，同请求头")
    # 这个字段属于敏感信息，在sdk中统一赋值
    customerId: Optional[str] = Field(None, description="客户ID，同请求头")

    # 使用 @model_validator 进行填充
    @model_validator(mode='before')
    @classmethod
    def set_defaults(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        # 自动生成 requestId 和自动填充 customerId
        if 'requestId' not in values:
            values['requestId'] = str(uuid.uuid4())  # 自动生成 requestId

        # 这个字段属于敏感信息，在sdk中统一赋值
        # if 'customerId' not in values:
        # values['customerId'] = 'default_customer_id'  # 填充 customerId，如果没有传入的话
        return values

    class Config:
        # 该配置项指定字符串（str 类型）最小长度为 1。这意味着如果在 Pydantic 模型中定义了一个字符串类型字段，并且该字段的值为空字符串（长度为 0），Pydantic 将抛出验证错误。
        # 例如，如果你在创建模型时传入空字符串，Pydantic 会报错：ValueError: ensure this value has at least 1 character.
        min_anystr_length = 1

        # 该配置项会自动去除传入字符串值的前后空白字符。这对于字符串字段非常有用，尤其是在用户输入时可能有额外的空格。
        # 例如，如果传入 " hello "，它将自动被处理为 "hello"。
        anystr_strip_whitespace = True

        # 该配置项使得在使用 enum 类型时，Pydantic 将使用 enum 的值（而不是 enum 的成员名）进行验证和序列化。
        # 如果您的模型字段是枚举类型（Enum），默认情况下 Pydantic 会使用 enum 成员的名称进行序列化。如果将该配置项设置为 True，它将使用枚举成员的值进行序列化。
        # 例如，假设有一个枚举类型 CardType，如果成员 VISA 对应的值是 'VISA'，那么序列化时将使用 'VISA'，而不是 'VISA' 的成员名称。
        use_enum_values = True

        # 这个配置项用于自定义 JSON 序列化的行为。它是一个字典，键是要自定义序列化的类型，值是一个函数，用来定义如何将该类型转换为 JSON 格式。
        # 在这个例子中，date 类型（例如 datetime.date 类型）将使用 isoformat() 方法进行序列化。isoformat() 会将日期格式化为 ISO 8601 标准格式，例如 2024-11-12。
        # 如果模型中有 date 类型字段，它将被转换为字符串格式 yyyy-MM-dd。
        json_encoders = {date: lambda v: v.isoformat()}  # 日期格式化
