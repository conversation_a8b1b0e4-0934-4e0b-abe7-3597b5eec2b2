from loguru import logger


class TriplinkintlClient:
    """
    底层 client 实现
    """

    def __init__(self, timeout=30):
        self.timeout = timeout
        self.response_headers = None

    async def send_by_curl_cffi(self, method, url, **kwargs):
        from curl_cffi import requests

        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.timeout

        resp_text = None
        async with requests.AsyncSession() as s:
            try:
                resp = await s.request(method, url, **kwargs)
                resp_text = resp.text
                self.response_headers = resp.headers
                # resp_cookies = resp.cookies.get_dict()
                # self.cookies.update(resp_cookies)
                # # logger.debug(resp_cookies)
                # self.cookies.update(s.cookies.get_dict())
                # # logger.debug(s.cookies.get_dict())

            except Exception as e:
                logger.exception(e)
                raise
            finally:
                logger.debug(
                    f'method {method}, url: {url}, kwargs: {kwargs}, resp_headers: {self.response_headers},  resp_text: {resp_text}'
                )

        return resp_text
