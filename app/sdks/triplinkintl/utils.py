import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad  # 导入 pad 和 unpad 方法
from Crypto.PublicKey import RSA
from Crypto.Signature import pkcs1_15
from Crypto.Hash import SHA256


class CipherUtils:

    @staticmethod
    def aes_encrypt(key_str: str, plaintext: str) -> str:
        # 解码密钥
        key = base64.b64decode(key_str)
        # 使用AES-128-ECB模式
        cipher = AES.new(key, AES.MODE_ECB)
        # 填充数据并加密
        ciphertext = cipher.encrypt(pad(plaintext.encode(), AES.block_size))
        # 返回Base64编码后的密文
        return base64.b64encode(ciphertext).decode('utf-8')

    @staticmethod
    def aes_decrypt(key_str: str, ciphertext: str) -> str:
        # 解码密钥和密文
        key = base64.b64decode(key_str)
        ciphertext_bytes = base64.b64decode(ciphertext)
        # 使用AES-128-ECB模式
        cipher = AES.new(key, AES.MODE_ECB)
        # 解密并去除填充
        decrypted_data = unpad(cipher.decrypt(ciphertext_bytes), AES.block_size)
        # 返回解密后的明文
        return decrypted_data.decode('utf-8')

    @staticmethod
    def rsa_sign(private_key_str: str, content: str) -> str:
        # 载入私钥
        private_key = RSA.import_key(CipherUtils.pem_format(private_key_str, 'PRIVATE KEY'))
        # 使用SHA256哈希
        hash_obj = SHA256.new(content.encode())
        # 生成签名
        signer = pkcs1_15.new(private_key)
        signature = signer.sign(hash_obj)
        # 返回Base64编码后的签名
        return base64.b64encode(signature).decode('utf-8')

    @staticmethod
    def rsa_verify(public_key_str: str, content: str, signature: str) -> bool:
        # 载入公钥
        public_key = RSA.import_key(CipherUtils.pem_format(public_key_str, 'PUBLIC KEY'))
        # 使用SHA256哈希
        hash_obj = SHA256.new(content.encode())
        # 验证签名
        signature_bytes = base64.b64decode(signature)
        try:
            pkcs1_15.new(public_key).verify(hash_obj, signature_bytes)
            return True
        except (ValueError, TypeError):
            return False

    @staticmethod
    def pem_format(key_str: str, label: str) -> str:
        # 将密钥格式化为PEM格式
        return f"-----BEGIN {label}-----\n{key_str}\n-----END {label}-----"
