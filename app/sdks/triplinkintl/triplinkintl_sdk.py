import time
from typing import Union
import uuid
from loguru import logger
import orjson
from pydantic import BaseModel
from app.sdks.triplinkintl.model.core.close_card_request import TriplinkintlCloseCardRequest
from app.sdks.triplinkintl.model.core.create_card_request import TriplinkintlCreateCardRequest
from app.sdks.triplinkintl.model.core.fx_quote_request import TriplinkintlFxQuoteRequest
from app.sdks.triplinkintl.triplinkintl_client import TriplinkintlClient
from app.sdks.triplinkintl.utils import CipherUtils


class TriplinkintlSdk:
    """
    triplinkintl SDK
    用于业务调用
    """

    def __init__(self, host, customer_id, aes_key, t_rsa_public_key, u_rsa_private_key):
        """_summary_

        Args:
            host (_type_): 接口地址
            customer_id (_type_): 客户ID
            aes_key (_type_): aes加密key
            t_rsa_public_key (_type_): 平台公钥，用于验证返回
            u_rsa_private_key (_type_): 用户私钥，用于签名
        """
        self.host = host
        self.customer_id = customer_id
        self.aes_key = aes_key
        self.t_rsa_public_key = t_rsa_public_key
        self.u_rsa_private_key = u_rsa_private_key

        self.client = TriplinkintlClient()

    def verify_request(self, request: dict) -> dict:
        """
        验证请求
        :param data:
        :return:
        """
        if not request.get('customerId'):
            request['customerId'] = self.customer_id
        return request

    def generate_headers(self, request_id: str, playload: str, service: str) -> dict:
        """
        生成请求头
        :param data:
        :return:
        """
        headers = {
            'customerId': self.customer_id,
            'service': service,
            'version': '2.0',
            'requestId': request_id,
            'timestamp': str(int(time.time() * 1000)),
        }
        content = '|'.join(
            [
                headers["customerId"],
                headers["service"],
                headers["version"],
                headers["requestId"],
                headers["timestamp"],
                playload,
            ]
        )
        signature = CipherUtils.rsa_sign(self.u_rsa_private_key, content)
        headers['sign'] = signature
        headers['Content-Type'] = 'application/json'
        return headers

    def verify_response(self, headers: dict, playload: str) -> dict:
        content = '|'.join(
            [
                headers["customerId"],
                headers["service"],
                headers["version"],
                headers["requestId"],
                str(headers["timestamp"]),
                headers["code"],
                headers["message"],
                playload,
            ]
        )
        signature = headers['sign']
        if not CipherUtils.rsa_verify(self.t_rsa_public_key, content, signature):
            logger.debug(f'headers: {content}, content: {signature}')
            raise Exception('接口返回签名验证失败')

    async def send(self, request: dict, service: str) -> dict:
        """
        发送请求
        :param data:
        :return:
        """
        # 补全request
        request = self.verify_request(request)
        logger.debug(request)
        # 对请求内容进行加密
        req_playload = CipherUtils.aes_encrypt(key_str=self.aes_key, plaintext=orjson.dumps(request).decode('utf-8'))
        # 生成请求头和签名
        headers = self.generate_headers(request_id=request['requestId'], playload=req_playload, service=service)
        data = {'payload': req_playload}

        resp_text = await self.client.send_by_curl_cffi(method='POST', url=self.host, headers=headers, json=data)

        # 判断请求成功与否
        resp_headers = self.client.response_headers
        logger.debug(resp_headers)
        if resp_headers.get('code') != '200':
            raise Exception(f'接口请求失败，code: {resp_headers.get("code")}, msg: {resp_headers.get("message")}')

        # 验证返回结果
        resp_json = orjson.loads(resp_text)
        self.verify_response(headers=resp_headers, playload=resp_json['payload'])

        # 解密返回内容
        result = orjson.loads(CipherUtils.aes_decrypt(key_str=self.aes_key, ciphertext=resp_json['payload']))
        logger.debug(result)
        if result.get('returnCode') != '000000':
            raise Exception(f'接口业务处理失败，code: {result.get("returnCode")}, msg: {result.get("errorMessage")}')
        return result

    async def create_card(self, request: TriplinkintlCreateCardRequest) -> dict:
        """
        创建卡
        :param data:
        :return:
        """
        # todo test
        # return {
        #     "returnCode": "000000",
        #     "errorMessage": "Success",
        #     "cardLogId": "9448b6a7b3bcb22f99c1bedd246aba092c7932fd5e7f3042607bf58bc7cc3d83",
        #     "cardNum": "****************",
        #     "cardExpirationDate": "2406",
        #     "cvv2": "123",
        #     "cardType": "GWTTP",
        #     "cardLabel": "MasterCard",
        # }
        return await self.send(request=request.model_dump(exclude_none=True, exclude_unset=True), service='createCard')

    async def fx_quote(self, request: TriplinkintlFxQuoteRequest) -> dict:
        """
        汇率查询
        :param data:
        :return:
        """
        return await self.send(request=request.model_dump(exclude_none=True, exclude_unset=True), service='fxQuote')

    async def close_card(self, request: TriplinkintlCloseCardRequest) -> dict:
        """
        关闭卡
        :param data:
        :return:
        """
        # todo test
        # return {"returnCode": "000000", "errorMessage": "Success"}
        return await self.send(request=request.model_dump(exclude_none=True, exclude_unset=True), service='closeCard')
