from enum import Enum, unique


@unique
class AirWallexAllowedTransactionCount(Enum):
    SINGLE = '单次'
    MULTIPLE = '多次'


@unique
class AirWallexCardStatus(Enum):
    # FAILED, INACTIVE, ACTIVE, LOST, STOLEN, CLOSED, BLOCKED, EXPIRED
    PENDING = '待激活'
    INACTIVE = '已失效'
    ACTIVE = '已激活'
    LOST = '已丢失'
    STOLEN = '已盗'
    CLOSED = '已关闭'
    BLOCKED = '已冻结'
    EXPIRED = '已过期'


@unique
class AirWallexFormFactor(Enum):
    VIRTUAL = '虚拟卡'
    PHYSICAL = '实体卡'


@unique
class AirWallexIssueTo(Enum):
    ORGANISATION = '机构'
    # lcc 采购不推荐个人卡
    # INDIVIDUAL = '个人'


@unique
class AirWallexTransactionIntervalLimit(Enum):
    # PER_TRANSACTION, DAILY, WEEKLY, MONTHLY, ALL_TIME
    PER_TRANSACTION = '单次'
    DAILY = '日限额'
    WEEKLY = '周限额'
    MONTHLY = '月限额'
    ALL_TIME = '总限额'
