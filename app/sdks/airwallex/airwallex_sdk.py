from datetime import datetime, timezone
import math
import time
from typing import Union
import uuid
from loguru import logger
import or<PERSON><PERSON>
from pydantic import BaseModel
from app.depends import redis_pool
from app.sdks.airwallex.airwallex_client import AirWallexClient
from app.sdks.airwallex.model.core.create_card_request import AirWallexCreateCardRequest
from app.sdks.airwallex.model.core.get_card_detail_request import (
    AirWallexGetCardDetailRequest,
    AirWallexGetCardSecretRequest,
)
from app.sdks.airwallex.model.core.login_request import AirWallexLoginRequest
from app.sdks.airwallex.model.core.update_card_request import AirWallexUpdateCardRequest


class AirWallexSdk:
    """
    airwallex SDK
    用于业务调用
    """

    def __init__(self, host: str, account_id: str, client_id: str, api_key: str):
        """_summary_

        Args:
            host (_type_): 接口地址
            account_id (_type_): 账户ID
            client_id (_type_): 客户ID
            api_key (_type_): API密钥
        """
        self.host = host
        self.account_id = account_id
        self.client_id = client_id
        self.api_key = api_key
        self.client = AirWallexClient(host=self.host)

    async def refresh_token(self, force: bool = False):
        token = None
        cache_key = f'airwallex_token'
        async with redis_pool as redis:
            token = await redis.get(cache_key)

            if not token or force:
                request = AirWallexLoginRequest()
                request._headers = {'x-api-key': self.api_key, 'x-client-id': self.client_id}
                resp = await self.client.send_async(request=request)
                token = resp.get('token')
                if token:
                    expire = resp.get('expires_at')
                    if expire:
                        # "2021-10-26T06:38:13+0000"
                        dt = datetime.strptime(expire, "%Y-%m-%dT%H:%M:%S%z")
                        logger.debug(f"UTC 时间: {dt}")
                        logger.debug(f"本地时间: {dt.astimezone()}")
                        expire = math.floor((dt.astimezone() - datetime.now(timezone.utc)).total_seconds()) - 60
                        logger.debug(expire)
                    else:
                        expire = 60 * 29
                    await redis.set(cache_key, token, ex=expire)
                else:
                    raise Exception(f'airwallex login failed: {resp}')

        return token

    async def create_card(self, request: AirWallexCreateCardRequest) -> dict:
        """
        创建卡
        :param data:
        :return:
        """
        # token = await self.refresh_token()
        # logger.debug(token)
        # request.headers['Authorization'] = f'Bearer {token}'
        result = await self.client.send_async(request=request)
        logger.debug(result)
        if result.get('code') and not result.get('card_id'):
            raise Exception(f'airwallex create card failed: {result}')
        return result

    async def get_card_detail(self, request: AirWallexGetCardDetailRequest) -> dict:
        """
        获取卡详情
        :param data:
        :return:
        """
        result = await self.client.send_async(request=request)
        logger.debug(result)
        if result.get('code') and not result.get('card_id'):
            raise Exception(f'airwallex get card detail failed: {result}')
        return result

    async def get_card_secret(self, request: AirWallexGetCardSecretRequest) -> dict:
        """
        获取卡详情
        :param data:
        :return:
        """
        result = await self.client.send_async(request=request)
        logger.debug(result)
        if result.get('code') and not result.get('card_id'):
            raise Exception(f'airwallex get card secret failed: {result}')
        return result

    async def update_card(self, request: AirWallexUpdateCardRequest) -> dict:
        """更新卡

        Args:
            request (AirWallexUpdateCardRequest): _description_

        Returns:
            dict: _description_
        """
        result = await self.client.send_async(request=request)
        logger.debug(result)
        if result.get('code') and not result.get('card_id'):
            raise Exception(f'airwallex update card failed: {result}')
        return result
