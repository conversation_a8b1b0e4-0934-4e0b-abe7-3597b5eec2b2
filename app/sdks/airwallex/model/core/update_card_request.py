from typing import ClassVar, Dict, Optional
from pydantic import Field
from pydantic.main import IncEx

from app.sdks.airwallex.model.core.create_card_request import AirWallexCreateCardRequest, AuthorizationControls, Program


class AirWallexUpdateCardRequest(AirWallexCreateCardRequest):
    """更新卡信息"""

    endpoint: ClassVar[str] = '/api/v1/issuing/cards/{card_id}/update'

    request_exclude: ClassVar[IncEx] = set(['card_id'])

    card_id: str = Field(..., description="卡ID")  # 通过request_exclude排除

    card_status: Optional[str] = Field(None, description="卡状态")

    authorization_controls: Optional[AuthorizationControls] = Field(None, description="授权控制")
    created_by: Optional[str] = Field(None, description="创建人")
    form_factor: Optional[str] = Field(None, description="表单因子")
    issue_to: Optional[str] = Field(None, description="发行给")
    is_personalized: Optional[bool] = Field(None, description="是否个人卡")
    program: Optional[Program] = Field(None, description="程序")
    purpose: Optional[str] = Field(None, description="目的")
