from typing import Any, ClassVar, Dict, List, Optional
from pydantic import BaseModel, Field

from app.sdks.airwallex.model.base_request import BaseRequest
from pydantic.main import IncEx

'''
# example

payload = "{
  "activate_on_issue": true,
  "authorization_controls": {
    "active_from": "2018-10-31T00:00:00+0800",
    "active_to": "2018-10-31T00:00:00+0800",
    "allowed_currencies": [
      "USD",
      "AUD"
    ],
    "allowed_merchant_categories": [
      "7531",
      "7534"
    ],
    "allowed_transaction_count": "SINGLE",
    "transaction_limits": {
      "cash_withdrawal_limits": [
        {
          "amount": 1000,
          "interval": "PER_TRANSACTION"
        }
      ],
      "currency": "USD",
      "limits": [
        {
          "amount": 1000,
          "interval": "PER_TRANSACTION"
        }
      ]
    }
  },
  "cardholder_id": "7f687fe6-dcf4-4462-92fa-80335301d9d2",
  "client_data": "20190817_dfelsflkj73494lksdfg9480ww",
  "created_by": "<PERSON>",
  "form_factor": "VIRTUAL",
  "issue_to": "ORGANISATION",
  "metadata": {
    "key1": "value1",
    "key2": "value2"
  },
  "nick_name": "travelling",
  "note": "This is my first card.",
  "postal_address": {
    "city": "Melbourne",
    "country": "AU",
    "line1": "44 Gillespie St",
    "line2": "Unit 2",
    "postcode": "3121",
    "state": "VIC"
  },
  "primary_contact_details": {
    "email": "<EMAIL>",
    "full_name": "John Smith",
    "mobile_number": "619922334321"
  },
  "purpose": "string",
  "request_id": "7f687fe6-dcf4-4462-92fa-80335301d9d2",
  "type": "PREPAID"
}"

'''


class AirWallexGetCardDetailRequest(BaseRequest):
    """
    获取卡片详情
    只有状态和持卡人信息
    """

    request_method: ClassVar[str] = 'GET'
    endpoint: ClassVar[str] = '/api/v1/issuing/cards/{card_id}'
    request_exclude: ClassVar[IncEx] = set(['card_id'])

    card_id: str = Field(..., description="卡ID")


class AirWallexGetCardSecretRequest(BaseRequest):
    """
    获取卡片详情（CVV）
    可以获取CVV
    """

    request_method: ClassVar[str] = 'GET'
    endpoint: ClassVar[str] = '/api/v1/issuing/cards/{card_id}/details'
    request_exclude: ClassVar[IncEx] = set(['card_id'])

    card_id: str = Field(..., description="卡ID")
