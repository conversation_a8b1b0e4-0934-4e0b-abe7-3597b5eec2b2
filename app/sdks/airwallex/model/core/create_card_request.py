from typing import Any, ClassVar, Dict, List, Optional
from pydantic import BaseModel, Field

from app.sdks.airwallex.consts import (
    AirWallexAllowedTransactionCount,
    AirWallexFormFactor,
    AirWallexIssueTo,
    AirWallexTransactionIntervalLimit,
)
from app.sdks.airwallex.model.base_request import GLOBAL_MODEL_CONFIG, BaseRequest

'''
# example

{
    "activate_on_issue": true,
    "additional_cardholder_ids": [
        "7f687fe6-dcf4-4462-92fa-80335301d9d2",
        "b0a1b145-4853-4456-b4b3-d690c7f3535c"
    ],
    "authorization_controls": {
        "active_from": "2018-10-31T00:00:00+0800",
        "active_to": "2018-10-31T00:00:00+0800",
        "allowed_currencies": [
            "USD",
            "AUD"
        ],
        "allowed_merchant_categories": [
            "7531",
            "7534"
        ],
        "allowed_transaction_count": "SINGLE",
        "transaction_limits": {
            "cash_withdrawal_limits": [
                {
                    "amount": 1000,
                    "interval": "PER_TRANSACTION"
                }
            ],
            "currency": "USD",
            "limits": [
                {
                    "amount": 1000,
                    "interval": "PER_TRANSACTION"
                }
            ]
        }
    },
    "brand": "VISA",
    "cardholder_id": "7f687fe6-dcf4-4462-92fa-80335301d9d2",
    "client_data": "20190817_dfelsflkj73494lksdfg9480ww",
    "created_by": "John Smith",
    "form_factor": "VIRTUAL",
    "is_personalized": true,
    "metadata": {
        "key1": "value1",
        "key2": "value2"
    },
    "nick_name": "travelling",
    "note": "This is my first card.",
    "postal_address": {
        "city": "Melbourne",
        "country": "AU",
        "line1": "44 Gillespie St",
        "line2": "Unit 2",
        "postcode": "3121",
        "state": "VIC"
    },
    "program": {
        "interchange_percent": "1.0",
        "purpose": "COMMERCIAL",
        "sub_type": "GOOD_FUNDS_CREDIT",
        "type": "PREPAID"
    },
    "purpose": "string",
    "request_id": "7f687fe6-dcf4-4462-92fa-80335301d9d2"
}

'''


class LimitElement(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    amount: float = Field(..., description="金额")
    interval: str = Field(AirWallexTransactionIntervalLimit.PER_TRANSACTION.name, description="间隔")


class TransactionLimits(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    cash_withdrawal_limits: List[LimitElement] = Field(None, description="提现限制")
    currency: Optional[str] = Field(None, description="币种")
    limits: List[LimitElement] = Field(..., description="限制")


class AuthorizationControls(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    active_from: Optional[str] = Field(None, description="授权开始时间")
    active_to: Optional[str] = Field(None, description="授权结束时间")
    allowed_currencies: Optional[List[str]] = Field(None, description="允许的币种")
    allowed_merchant_categories: Optional[List[str]] = Field(None, description="允许的商户类别")
    allowed_transaction_count: str = Field(AirWallexAllowedTransactionCount.SINGLE.name, description="允许的交易次数")
    transaction_limits: TransactionLimits = Field(..., description="交易限制")


class Program(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    interchange_percent: Optional[str] = Field(None, description="交换百分比")
    purpose: Optional[str] = Field('COMMERCIAL', description="目的")
    sub_type: str = Field('GOOD_FUNDS_CREDIT', description="子类型")
    type: str = Field('CREDIT', description="类型")


class AirWallexCreateCardRequest(BaseRequest):
    """创建卡"""

    endpoint: ClassVar[str] = '/api/v1/issuing/cards/create'

    request_id: str = Field(..., description="请求ID")

    activate_on_issue: bool = Field(None, description="是否在发卡时激活卡")
    additional_cardholder_ids: Optional[List[str]] = Field(None, description="附加持卡人ID")

    authorization_controls: AuthorizationControls = Field(..., description="授权控制")
    brand: Optional[str] = Field(None, description="品牌")
    cardholder_id: str = Field(None, description="持卡人ID")
    client_data: Optional[str] = Field(None, description="客户端数据")
    created_by: str = Field(..., description="创建人")
    form_factor: str = Field(AirWallexFormFactor.VIRTUAL.name, description="表单因子")
    issue_to: str = Field(AirWallexIssueTo.ORGANISATION.name, description="发行给")
    is_personalized: bool = Field(True, description="是否个人卡")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")
    nick_name: Optional[str] = Field(None, description="昵称")
    note: Optional[str] = Field(None, description="备注")
    postal_address: Optional[Dict[str, Any]] = Field(None, description="邮政地址")

    program: Program = Field(..., description="程序")
    purpose: Optional[str] = Field('ONLINE_PURCHASING', description="目的")
