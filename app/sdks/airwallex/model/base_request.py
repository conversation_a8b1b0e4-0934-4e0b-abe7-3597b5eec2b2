from datetime import date
from enum import Enum
import re
from typing import Any, ClassVar, Dict, Optional
import uuid
from loguru import logger
from pydantic import BaseModel, ConfigDict, Field, PrivateAttr, model_validator
from pydantic.main import IncEx

# 并不是所有model都派生于BaseRequest
GLOBAL_MODEL_CONFIG = ConfigDict(
    # 不允许空字符串
    min_extra_fields=1,
    # 自动去除空格
    anystr_strip_whitespace=True,
    #
    json_encoders={date: lambda v: v.isoformat()},
)


class BaseRequest(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG
    # ClassVar 不会被当做模型字段
    # 可用来定义常量
    # 基类保留的字段
    request_method: ClassVar[str] = 'POST'
    endpoint: ClassVar[str] = '/'
    content_type: ClassVar[str] = 'json'
    encrypt: ClassVar[bool] = False
    request_exclude: ClassVar[IncEx] = None

    # 接口通用属性
    # 通过 exclude=True 从 model_dump 中排除
    _headers: Dict[str, Any] = PrivateAttr(default_factory=dict)

    # 使用 @model_validator 进行填充
    @model_validator(mode='before')
    @classmethod
    def set_defaults(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        # 自动生成 requestId 和自动填充 customerId
        if 'request_id' not in values:
            values['request_id'] = str(uuid.uuid4())  # 自动生成 requestId

        return values

    def get_url_path(self) -> str:
        """
        动态替换 URL 模板中的占位符。
        """
        endpoint = self.endpoint

        # placeholders = re.findall(r'{(.*?)}', endpoint)

        # if not placeholders:
        #     return endpoint

        # replacements = {}
        # for field in placeholders:
        #     value = getattr(self, field, None)
        #     if value is None:
        #         raise ValueError(f"占位符 '{field}' 的值未定义，无法填充 URL。")
        #     replacements[field] = value

        # if not replacements:

        try:
            # 这里改利用request_exclude进行控制
            # 比起在字段上直接设置exclude=True的方法，更加灵活
            # 比如有的字段只出现在url，有的字段既出现在url，又出现在字段中

            # 在填充url的时候，不需要填充空值
            # 同时不设置exclude_unset=True，避免默认值被忽略
            # 不设置exclude
            replacements = self.model_dump(exclude_none=True)
            endpoint = endpoint.format_map(replacements)
        except KeyError as e:
            raise ValueError(f"Error formatting URL: Missing value for placeholder {e}")
        return endpoint

    def request_kwargs_dump(self, host):
        request_dict = {'url': f'{host}{self.get_url_path()}'}
        if self._headers:
            request_dict['headers'] = self._headers
        # exclude_unset=True 会导致 使用默认值的字段被忽略
        # 所以这里不要设置 exclude_unset=True
        # 这里同时设置了exclude，排除了request_exclude中仅做url填充的字段
        logger.debug(f'kwargs: {self.request_exclude}')
        send_data = self.model_dump(exclude_none=True, exclude=self.request_exclude)
        if self.request_method.upper() == 'POST':
            if self.content_type == 'json':
                request_dict['json'] = send_data
            else:
                request_dict['data'] = send_data
        elif self.request_method.upper() == 'GET':
            request_dict['params'] = send_data
        else:
            logger.info(f"{self.request_method} 参数默认用data发送")
            request_dict['data'] = send_data
        return request_dict


class SdkClient:
    def __init__(self, host: str, **kwargs):
        self.host = host
        self.aes_key = kwargs.get('aes_key')
        self.timeout = kwargs.get('timeout', 30)

    def send(self, request: BaseRequest):
        import requests

        kwargs = request.request_kwargs_dump(self.host)
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.timeout
        result = None
        try:
            response = requests.request(request.request_method.upper(), **kwargs)
            result = response.json()
            self.response_headers = response.headers
        except Exception as e:
            logger.exception(e)
            raise
        finally:
            logger.debug(f'kwargs: {kwargs}, resp_headers: {self.response_headers},  response: {result}')
        return result

    async def send_async(self, request: BaseRequest):
        from curl_cffi import requests

        kwargs = request.request_kwargs_dump(self.host)
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.timeout
        result = None
        async with requests.AsyncSession() as s:
            try:
                response = await s.request(request.request_method.upper(), **kwargs)
                result = response.json()
                self.response_headers = response.headers
            except Exception as e:
                logger.exception(e)
                raise
            finally:
                logger.debug(f'kwargs: {kwargs}, resp_headers: {self.response_headers},  response: {result}')

        return result
