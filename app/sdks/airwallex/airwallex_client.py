from loguru import logger

from app.sdks.airwallex.model.base_request import BaseRequest


class AirWallexClient:
    """
    底层 client 实现
    """

    def __init__(self, host: str, timeout=30):
        self.host = host
        self.timeout = timeout
        self.response_headers = None

    def send(self, request: BaseRequest):
        import requests

        kwargs = request.request_kwargs_dump(self.host)
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.timeout
        result = None
        try:
            response = requests.request(request.request_method.upper(), **kwargs)
            result = response.json()
            self.response_headers = response.headers
        except Exception as e:
            logger.exception(e)
            raise
        finally:
            logger.debug(f'kwargs: {kwargs}, resp_headers: {self.response_headers},  response: {result}')
        return result

    async def send_async(self, request: BaseRequest):
        from curl_cffi import requests

        kwargs = request.request_kwargs_dump(self.host)
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.timeout
        result = None
        async with requests.AsyncSession() as s:
            try:
                response = await s.request(request.request_method.upper(), **kwargs)
                result = response.json()
                self.response_headers = response.headers
            except Exception as e:
                logger.exception(e)
                raise
            finally:
                logger.debug(f'kwargs: {kwargs}, resp_headers: {self.response_headers},  response: {result}')

        return result
