import orjson
from commons.cores.base_client import BaseAsyncClient


class FlightOrderSdk(BaseAsyncClient):

    def __init__(self, host, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.host = host

    @property
    def default_headers(self):
        return {'Content-Type': 'application/json'}

    async def book_start(self, data, callback_url: str = None):
        if callback_url:
            url = callback_url
        else:
            url = f'{self.host}/api/v1/flight_order/public/order/pay/start'

        resp_text = await self._request('POST', url, json=data)
        resp_json = orjson.loads(resp_text)
        return resp_json

    async def book_callback(self, data, callback_url: str = None):
        if callback_url:
            url = callback_url
        else:
            url = f'{self.host}/api/v1/flight_order/crawler/callback/book/result'
        resp_text = await self._request('POST', url, json=data)
        resp_json = orjson.loads(resp_text)
        return resp_json

    async def pay_start(self, data, callback_url: str = None):
        if callback_url:
            url = callback_url
        else:
            url = f'{self.host}/api/v1/flight_order/public/order/pay/start'

        resp_text = await self._request('POST', url, json=data)
        resp_json = orjson.loads(resp_text)
        return resp_json

    async def pay_callback(self, data, callback_url: str = None):
        if callback_url:
            url = callback_url
        else:
            url = f'{self.host}/api/v1/flight_order/crawler/callback/pay/result'
        resp_text = await self._request('POST', url, json=data)
        resp_json = orjson.loads(resp_text)
        return resp_json

    async def check_callback(self, data, callback_url: str = None):
        if callback_url:
            url = callback_url
        else:
            url = f'{self.host}/api/v1/flight_order/crawler/callback/check/result'
        resp_text = await self._request('POST', url, json=data)
        resp_json = orjson.loads(resp_text)
        return resp_json
