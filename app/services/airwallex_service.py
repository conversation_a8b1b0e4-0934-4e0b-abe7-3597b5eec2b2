import asyncio
from datetime import datetime, timedelta
import math
import random

from loguru import logger
import orj<PERSON>
from app.consts.currency_codes import Cur<PERSON>cyISO
from app.consts.status import CardStatus
from app.consts.supplier_codes import SupplierCodes

from app.sdks.airwallex.consts import (
    AirWallexAllowedTransactionCount,
    AirWallexCardStatus,
    AirWallexFormFactor,
    AirWallexIssueTo,
    AirWallexTransactionIntervalLimit,
)
from app.sdks.airwallex.model.core.get_card_detail_request import (
    AirWallexGetCardDetailRequest,
    AirWallexGetCardSecretRequest,
)

from app.sdks.airwallex.model.core.create_card_request import AirWallexCreateCardRequest
from app.sdks.airwallex.airwallex_sdk import AirWallexSdk
from app.sdks.airwallex.model.core.update_card_request import AirWallexUpdateCardRequest
from app.depends import ex_rate_service
from app.config import settings


awx_sdk = AirWallexSdk(
    host=settings.AIRWALLEX_HOST,
    account_id=settings.AIRWALLEX_ACCOUNT_ID,
    client_id=settings.AIRWALLEX_CLIENT_ID,
    api_key=settings.AIRWALLEX_API_KEY,
)


async def create_card(
    order_no: str, airline_code: str, currency_code: str, card_limit: float, channel_type: str = 'web'
):
    now_time = datetime.now()
    # 明天
    tomorrow = now_time + timedelta(days=1)

    logger.debug(settings.AIRLINE_CARD_MAP[airline_code])
    airline_card_code = random.choice(settings.AIRLINE_CARD_MAP[airline_code])

    card_limit_cfg = settings.CARD_LIMIT_CONFIG[airline_code]

    # 接口传入优先级高于配置
    # if not currency_code:
    #     currency_code = card_limit_cfg['currency']

    # cny_limit = card_limit_cfg['cny_limit']

    logger.debug(airline_card_code)

    # 创建卡
    token = await awx_sdk.refresh_token()

    logger.debug(f'{currency_code}:{CurrencyISO[currency_code].value}')
    await ex_rate_service.auto_refresh()
    rate = ex_rate_service.get_rate(from_code=currency_code, to_code='CNY')
    cny_limit = math.ceil(card_limit * rate['rateData']) + settings.CARD_LIMIT_OFFSET
    create_request = AirWallexCreateCardRequest(
        authorization_controls={
            "allowed_currencies": [currency_code],
            "allowed_transaction_count": AirWallexAllowedTransactionCount.SINGLE.name,
            "transaction_limits": {
                "currency": "CNY",
                "limits": [{"amount": cny_limit, "interval": AirWallexTransactionIntervalLimit.PER_TRANSACTION.name}],
            },
        },
        cardholder_id=settings.AIRWALLEX_ACCOUNT_ID,
        created_by=settings.AIRWALLEX_ACCOUNT,
        form_factor=AirWallexFormFactor.VIRTUAL.name,
        issue_to=AirWallexIssueTo.ORGANISATION.name,
        program={"type": "CREDIT", "sub_type": "GOOD_FUNDS_CREDIT"},
        is_personalized=False,
    )
    create_request._headers = {'Authorization': f'Bearer {token}'}

    # 如果是物理卡
    # 则发卡时立即激活
    if create_request.form_factor == AirWallexFormFactor.PHYSICAL.name:
        create_request.activate_on_issue = True

    # create_request.headers = {'Authorization': f'Bearer {token}'}
    create_result = await awx_sdk.create_card(create_request)
    card_id = create_result['card_id']
    asyncio.sleep(random.randint(1, 2))
    card_status = CardStatus.PENDING.value
    # 获取卡详情
    detail_request = AirWallexGetCardDetailRequest(card_id=card_id)
    detail_request._headers = {'Authorization': f'Bearer {token}'}
    for i in range(3):
        detail_result = await awx_sdk.get_card_detail(request=detail_request)
        if detail_result['card_status'] == AirWallexCardStatus.ACTIVE.name:
            card_status = CardStatus.ACTIVE.value
            break

    secret_request = AirWallexGetCardSecretRequest(card_id=card_id)
    secret_request._headers = {'Authorization': f'Bearer {token}'}
    secret_result = await awx_sdk.get_card_secret(request=secret_request)
    exp_date = str(secret_result['expiry_year'])[-2:] + str(secret_result['expiry_month']).zfill(2)

    new_row = dict(
        order_no=order_no,
        supplier=SupplierCodes.AIRWALLEX.value,
        # 卡信息
        supplier_card_id=card_id,
        card_no=secret_result['card_number'],
        card_expiration_date=exp_date,
        cvv2=secret_result['cvv'],
        card_type=create_result['form_factor'],
        card_label=create_result['brand'],
        # 授权和限制信息
        currency_code=currency_code,
        card_limit=cny_limit,
        min_auth_amount=cny_limit,
        max_auth_amount=cny_limit,
        max_auth_times=1,
        card_close_usage=100,
        # 卡状态
        status=card_status,
        # 创建信息
        create_request_snapshot=orjson.dumps(create_request.model_dump(exclude_none=True, exclude_unset=True)).decode(
            'utf-8'
        ),
        create_response_snapshot=orjson.dumps(create_result).decode('utf-8'),
    )

    return new_row


async def close_card(supplier_card_id: str):

    # 创建卡
    token = await awx_sdk.refresh_token()

    request = AirWallexUpdateCardRequest(card_id=supplier_card_id, card_status=AirWallexCardStatus.CLOSED.name)
    request._headers = {'Authorization': f'Bearer {token}'}

    result = await awx_sdk.update_card(request)
    return True
