from datetime import datetime, timed<PERSON>ta
import math
import os
import random

from loguru import logger
import orj<PERSON>
from app.consts.currency_codes import CurrencyISO
from app.consts.status import CardStatus
from app.consts.supplier_codes import SupplierCodes
from app.models.pay_card import PayCard
from app.sdks.triplinkintl.model.core.close_card_request import TriplinkintlCloseCardRequest
from app.sdks.triplinkintl.model.core.create_card_request import TriplinkintlCreateCardRequest
from app.sdks.triplinkintl.triplinkintl_sdk import TriplinkintlSdk

from app.views.schemas import public_schemas
from commons.consts.common_status import EnableStatus
from app.config import settings, new_settings
from app.depends import ex_rate_service


async def create_card(
    order_no: str, airline_code: str, currency_code: str = None, card_limit: int = None, channel_type: str = 'web'
):

    now_time = datetime.now()
    # 明天
    tomorrow = now_time + timedelta(days=1)

    logger.debug(settings.AIRLINE_CARD_MAP[airline_code])
    airline_card_code = random.choice(settings.AIRLINE_CARD_MAP[airline_code])
    logger.debug(airline_card_code)

    card_limit_cfg = settings.CARD_LIMIT_CONFIG[airline_code]

    # 接口传入优先级高于配置
    # if not currency_code:
    #     currency_code = card_limit_cfg['currency']

    triplink_settlement_currency_code = card_limit_cfg['triplink_settlement_currency_code']

    limit_times = card_limit_cfg.get('limit_times', 1)

    await ex_rate_service.auto_refresh()
    # 计算支付币种与结算币种的汇率
    # rate = ex_rate_service.get_rate(from_code=currency_code, to_code=triplink_settlement_currency_code)
    # 按结算币种计算限额，加上一个偏移量，避免汇率波动导致限额不足
    # card_limit = math.ceil(card_limit * rate['rateData']) + settings.CARD_LIMIT_OFFSET
    card_limit = math.ceil(card_limit) + settings.CARD_LIMIT_OFFSET
    # logger.debug(rate)
    logger.debug(card_limit)
    product_cfg = new_settings.get(f'triplinkintl.product.{airline_code}.{channel_type}')
    # product_map = settings.TRIPLINKINTL_CARD_MAP[airline_card_code]
    product_code = product_cfg['code']
    card_type = product_cfg['type']
    card_label = product_cfg['label']
    card_bin = random.choice(product_cfg.get('bins', [None]))

    logger.debug(f'{currency_code}:{CurrencyISO[currency_code].value}')
    request = TriplinkintlCreateCardRequest(
        supportedMccGroup=settings.TRIPLINKINTL_SUPPORTED_MCC_GROUP,  # todo 需要TRIPLINKINTL 定义
        cardCurrencyCode=CurrencyISO[currency_code].value,  # 卡币种（与航司交易时的币种）
        settlementCurrencyCode=CurrencyISO[
            triplink_settlement_currency_code
        ].value,  # 结算币种（与triplink结算时的币种）
        activeDate=now_time.strftime("%Y-%m-%d"),  # 生效日期
        inactiveDate=tomorrow.strftime("%Y-%m-%d"),  # 失效日期
        cardLimit=card_limit,  # 限额，对应卡币种
        minAuthAmount=0,  # 最低授权额度，对应卡币种
        maxAuthAmount=card_limit,  # 最高授权额度，对应卡币种
        maxAuthTimes=limit_times,
        cardProductCode=product_code,
        cardType=card_type,
        cardLabel=card_label,
    )
    if card_bin:
        request.cardBin = card_bin
    tli_sdk = TriplinkintlSdk(
        host=settings.TRIPLINKINTL_HOST,
        customer_id=settings.TRIPLINKINTL_CUSTOMER_ID,
        aes_key=settings.TRIPLINKINTL_AES_KEY,
        t_rsa_public_key=settings.TRIPLINKINTL_T_RSA_PUBLIC_KEY,
        u_rsa_private_key=settings.TRIPLINKINTL_U_RSA_PRIVATE_KEY,
    )
    create_result = await tli_sdk.create_card(request)
    new_row = dict(
        order_no=order_no,
        supplier=SupplierCodes.TRIPLINKINTL.value,
        # 卡信息
        supplier_card_id=create_result['cardLogId'],
        card_no=create_result['cardNum'],
        card_expiration_date=create_result['cardExpirationDate'],
        cvv2=create_result['cvv2'],
        card_type=create_result['cardType'],
        card_label=create_result['cardLabel'],
        # 授权和限制信息
        currency_code=currency_code,
        card_limit=request.cardLimit,
        min_auth_amount=request.minAuthAmount,
        max_auth_amount=request.maxAuthAmount,
        max_auth_times=request.maxAuthTimes,
        card_close_usage=request.cardCloseUsage,
        # 卡状态
        status=CardStatus.ACTIVE.value,
        # 创建信息
        create_request_snapshot=orjson.dumps(request.model_dump(exclude_none=True, exclude_unset=True)).decode('utf-8'),
        create_response_snapshot=orjson.dumps(create_result).decode('utf-8'),
    )

    return new_row


async def close_card(supplier_card_id: str):
    request = TriplinkintlCloseCardRequest(cardLogId=supplier_card_id)
    tli_sdk = TriplinkintlSdk(
        host=settings.TRIPLINKINTL_HOST,
        customer_id=settings.TRIPLINKINTL_CUSTOMER_ID,
        aes_key=settings.TRIPLINKINTL_AES_KEY,
        t_rsa_public_key=settings.TRIPLINKINTL_T_RSA_PUBLIC_KEY,
        u_rsa_private_key=settings.TRIPLINKINTL_U_RSA_PRIVATE_KEY,
    )
    try:
        close_result = await tli_sdk.close_card(request)
        return True
    except Exception as e:
        logger.warning(e)
        # 卡片已注销
        if "code: 200025, msg: card status pre cancel not allow" in str(e):
            return True
        return False
