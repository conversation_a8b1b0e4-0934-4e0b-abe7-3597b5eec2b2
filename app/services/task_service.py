import asyncio
from datetime import datetime, timedelta
import random

from loguru import logger
import or<PERSON><PERSON>
from app.consts.currency_codes import C<PERSON><PERSON>cyISO
from app.consts.status import CardStatus
from app.consts.supplier_codes import SupplierCodes
from app.models.pay_card import PayCard
from app.sdks.triplinkintl.model.core.create_card_request import TriplinkintlCreateCardRequest
from app.sdks.triplinkintl.triplinkintl_sdk import TriplinkintlSdk
from app.services import airwallex_service, triplinkintl_service
from app.views.schemas import public_schemas
from commons.consts.common_status import EnableStatus
from app.config import settings


async def close_pay_card(inteval: int, minutes: int):
    while True:
        try:
            pay_card_rows = await PayCard.get_all_async(
                PayCard.status != CardStatus.CLOSED.value, PayCard.flight_dep_time < str(datetime.today())
            )
            for pay_card_row in pay_card_rows:
                is_close = False
                if pay_card_row['supplier'] == SupplierCodes.TRIPLINKINTL.value:
                    is_close = await triplinkintl_service.close_card(supplier_card_id=pay_card_row['supplier_card_id'])
                elif pay_card_row['supplier'] == SupplierCodes.AIRWALLEX.value:
                    is_close = await airwallex_service.close_card(supplier_card_id=pay_card_row['supplier_card_id'])
                if is_close:
                    await PayCard.update_by_async(PayCard.id == pay_card_row['id'], status=CardStatus.CLOSED.value)
        except Exception as e:
            logger.error(str(e))
        finally:
            await asyncio.sleep(inteval)
