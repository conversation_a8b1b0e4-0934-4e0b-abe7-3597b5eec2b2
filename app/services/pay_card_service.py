from datetime import datetime, timedelta
import math
import os
import random

from loguru import logger
import orjson
from app.consts.supplier_codes import SupplierCodes
from app.models.pay_card import PayCard
from app.services import airwallex_service, exchange_rate_service, triplinkintl_service
from app.views.schemas import public_schemas
from app.config import settings


async def grenade_card(
    order_no: str,
    airline_code: str,
    flight_dep_time: str,
    currency_code: str,
    card_limit: float,
    supplier_code: str = None,
    admin_id: int = 0,
    username: str = '',
    remark: str = '',
    channel_type: str = 'web',
):
    """_summary_

    Args:
        order_no (str): 订单号
        airline_code (str): 航空公司代码
        flight_dep_time (str): 航班出发时间
        currency_code (str): 支付币种
        card_limit (int, optional): 卡限额. Defaults to None.
        supplier_code (str, optional): 供应商代码. Defaults to None.
        admin_id (int, optional): 管理员ID. Defaults to 0.
        username (str, optional): 用户名. Defaults to ''.
        remark (str, optional): _description_. Defaults to ''.

    Raises:
        Exception: _description_
        Exception: _description_
        Exception: _description_

    Returns:
        _type_: _description_
    """
    new_row = {}
    # 改成每次都创建新卡，便于简化
    logger.debug(supplier_code)
    logger.debug(settings.SUPPLIERS)

    if supplier_code and supplier_code not in settings.SUPPLIERS:
        raise Exception(f'暂不支持该供应商的支付卡: {supplier_code}')
    elif not settings.SUPPLIERS:
        raise Exception('未配置可用的支付供应商')
    elif not supplier_code:
        supplier_code = random.choice(settings.SUPPLIERS)

    if supplier_code == SupplierCodes.TRIPLINKINTL.value:
        new_row = await triplinkintl_service.create_card(
            order_no=order_no,
            airline_code=airline_code,
            currency_code=currency_code,
            card_limit=card_limit,
            channel_type=channel_type,
        )
    elif supplier_code == SupplierCodes.AIRWALLEX.value:
        new_row = await airwallex_service.create_card(
            order_no=order_no,
            airline_code=airline_code,
            currency_code=currency_code,
            card_limit=card_limit,
            channel_type=channel_type,
        )
    else:
        raise Exception(f'暂不支持该供应商的支付卡: {supplier_code}')
    new_row['flight_dep_time'] = flight_dep_time
    new_row['admin_id'] = admin_id
    new_row['username'] = username
    new_row['remark'] = remark
    pay_card_row = await PayCard.create_at_async(**new_row)
    return pay_card_row


async def get_card_list(order_no: str):
    pay_card_rows = await PayCard.get_all_async(PayCard.order_no == order_no, order_by=PayCard.created.desc())

    return pay_card_rows
