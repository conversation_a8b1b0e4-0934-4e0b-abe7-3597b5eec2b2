from datetime import datetime, timedelta
from math import ceil
import os
import time
import aiofiles
from loguru import logger
import orjson
from pydantic import BaseModel
import requests
from app.config import settings


class UnionpayintlExchangeRateServices:
    """银联汇率服务"""

    def __init__(self, json_file: str = None) -> None:
        self.json_file = json_file
        self._rate_map = {}

    async def auto_refresh(self, path: str = None) -> None:
        if path is None:
            path = self.json_file

        today = datetime.now()

        # 内存数据已是最新
        if self._rate_map and self._rate_map.get('curDate') == today.strftime("%Y-%m-%d"):
            return

        # 文件是否存在
        file_valid = os.path.exists(path)
        if file_valid:
            # 文件是否更新
            file_valid = datetime.fromtimestamp(os.path.getmtime(path)).strftime("%Y%m%d") == today.strftime("%Y%m%d")

        if not file_valid:
            # 从网络获取
            for i in range(2):
                resp = requests.get(f'https://m.unionpayintl.com/jfimg/{today}.json')
                try:
                    self._rate_map = resp.json()
                    # 覆盖，没有则创建
                    async with aiofiles.open(path, 'w') as f:
                        await f.write(resp.text)
                    break
                except Exception as e:
                    logger.exception(e)
                # 出现汇率没更新，退回上一天
                today = (datetime.now() - timedelta(days=i + 1)).strftime("%Y%m%d")
        else:
            async with aiofiles.open(path, 'r') as f:
                self._rate_map = orjson.loads(await f.read())

    def get_rate(self, from_code: str, to_code: str) -> float:
        ze_rate = None
        for rate in self._rate_map['exchangeRateJson']:
            if rate['transCur'] == from_code and rate['baseCur'] == to_code:
                return rate
            if rate['transCur'] == to_code and rate['baseCur'] == from_code:
                ze_rate = rate
        else:
            if ze_rate:
                # 逆向汇率计算
                logger.debug(f'find reverse rate from {to_code} to {from_code} {ze_rate}')
                logger.debug(round(ze_rate['rateData'], 4))
                logger.debug(1 / round(ze_rate['rateData'], 4))
                return {'transCur': from_code, 'baseCur': to_code, 'rateData': 1 / round(ze_rate['rateData'], 4)}
            else:
                logger.error(f'cannot find rate from {from_code} to {to_code}')
                return None

    def compute(self, from_code: str, to_code: str, amount: float, decimal_places: int = 0) -> float:
        trans_amount = amount
        rate = self.get_rate(from_code=from_code, to_code=to_code)
        logger.debug(rate)
        if rate is not None:
            if decimal_places:
                trans_amount = pow(10, decimal_places) * amount
                trans_amount = ceil(trans_amount * rate['rateData']) / pow(10, decimal_places)
            else:
                trans_amount = float(ceil(amount * rate['rateData']))
        logger.debug(trans_amount)
        return trans_amount


class ChinamoneyExchangeRateServices:
    """中国银行汇率服务"""

    def __init__(self, json_file: str = None) -> None:
        self.json_file = json_file
        self._rate_map = {}

    async def auto_refresh(self, path: str = None) -> None:
        if path is None:
            path = self.json_file

        today = datetime.now()
        # 内存数据已是最新
        if self._rate_map and today.strftime("%Y-%m-%d") in self._rate_map.get('data', {}).get('lastDate', ''):
            logger.debug(f'{today.strftime("%Y-%m-%d")} is the latest date')
            return

        # 文件是否存在
        file_valid = os.path.exists(path)
        if file_valid:
            # 文件是否更新
            file_valid = datetime.fromtimestamp(os.path.getmtime(path)).strftime("%Y%m%d") == today.strftime("%Y%m%d")

        if not file_valid:
            # 从网络获取

            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'DNT': '1',
                'Pragma': 'no-cache',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
                'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"macOS"',
            }
            for i in range(2):
                params = {'t': f'{int(time.time() * 1000)}'}

                resp = requests.post(
                    # 'https://www.chinamoney.com.cn/r/cms/www/chinamoney/data/fx/sdds-exch-rate.json',
                    'https://www.chinamoney.com.cn/r/cms/www/chinamoney/data/fx/crr.json',
                    params=params,
                    headers=headers,
                    verify=False,
                )
                try:
                    logger.debug(resp.text)
                    self._rate_map = resp.json()
                    # 覆盖，没有则创建
                    async with aiofiles.open(path, 'w') as f:
                        await f.write(resp.text)
                    break
                except Exception as e:
                    logger.exception(e)
        else:
            async with aiofiles.open(path, 'r') as f:
                self._rate_map = orjson.loads(await f.read())

    def get_rate(self, from_code: str, to_code: str) -> float:
        records = self._rate_map['records']
        key = f'{from_code}/{to_code}'
        rt = [x for x in records if x['ccypair'] == key]
        if rt:
            # return round(float(rt[0]['price']), 6)
            return {'transCur': from_code, 'baseCur': to_code, 'rateData': round(float(rt[0]['rate']), 6)}
        else:
            key2 = f'{to_code}/{from_code}'
            rt = [x for x in records if x['ccypair'] == key2]
            if rt:
                # return round(1 / float(rt[0]['price']), 6)
                return {'transCur': from_code, 'baseCur': to_code, 'rateData': 1 / round(float(rt[0]['rate']), 6)}
            else:
                logger.error(f'cannot find rate from {from_code} to {to_code}')
                return None

    def compute(self, from_code: str, to_code: str, amount: float, decimal_places: int = 0) -> float:
        trans_amount = amount
        rate = self.get_rate(from_code=from_code, to_code=to_code)
        logger.debug(rate)
        if rate is not None:
            if decimal_places:
                trans_amount = pow(10, decimal_places) * amount
                trans_amount = ceil(trans_amount * rate['rateData']) / pow(10, decimal_places)
            else:
                trans_amount = float(ceil(amount * rate['rateData']))
        logger.debug(trans_amount)
        return trans_amount
