import asyncio
from app.config import settings
from loguru import logger
import typer
from app.api import fast_api_app

cli = typer.Typer()


# @cli.command()
# def api(host: str = '0.0.0.0', port: int = 8080):
#     """运行api

#     开发调试
#     """
#     import uvicorn


#     uvicorn.run(fast_api_app, host=host, port=port)
@cli.command()
def close_pay_card(inteval: int = 30, minutes: int = 5):
    from app.services import task_service

    asyncio.run(task_service.close_pay_card(inteval=inteval, minutes=minutes))


@cli.command()
def debug():
    """调试"""
    try:
        logger.debug(settings)
        from Crypto.Random import get_random_bytes
        import base64

        # 生成128位（16字节）的AES密钥
        aes_key = get_random_bytes(10)  # 16字节 = 128位
        '''

        '''
        # 对AES密钥进行Base64编码
        aes_key_base64 = base64.b64encode(aes_key).decode('utf-8')
        if len(aes_key_base64) != 16:
            raise ValueError("Generated key is not 16 characters long")
        print("Base64 Encoded AES 128-bit Key:", aes_key_base64)
    except Exception as e:
        logger.exception(e)


if __name__ == '__main__':
    cli()
