import base64
from datetime import datetime, timedelta
import os

from loguru import logger
from app.config import settings
from app.sdks.triplinkintl.utils import CipherUtils


# @pytest.mark.asyncio
def test_aes():
    key_str = settings.TRIPLINKINTL_AES_KEY
    plaintext = "This is a secret message."

    # AES加密
    ciphertext = CipherUtils.aes_encrypt(key_str, plaintext)
    logger.debug(f"AES Encrypted: {ciphertext}")

    # AES解密
    decrypted_text = CipherUtils.aes_decrypt(key_str, ciphertext)
    logger.debug(f"AES Decrypted: {decrypted_text}")
    assert decrypted_text == plaintext


def test_rsa():
    private_key = """MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDaAkxUTbVEktABj/UygARAF1wyv5FMWYj580RbYITPZAfzLhCahF0YaPT6r/LSEt9dj2XigrM05OjmdnCNcQ79t5aHmBGRIwB/uGpsBQlJhA/nklR6fHIOKS2DyznSrhm4vbRcWmiZ9ML+6YfRFSKJTBM+Xy7MIX292n2CU7hWryvb0RyPqHS6Ps0FJXTyvizU+kjc3zoq9GSnMK0MAPWcAN9C1/Q9On2q55Q4NyTfoUubVO9eZ0hXW+PFZAwouzRawXIAOTyrP4oQ07ponbk2egA1m52vykLF6YuhcZqcTbn+v+4QSEtuXhPmQgRPayPdbosDqfRsmlyH5eOWao1pAgMBAAECggEAXcJWRH2FI4BGgB2DFRx0d4H0b4ju+tiY7fJ/KBNrGuyI9L+PeXzKsvhUv8nTEyD1un21EkUmwG1gctlmT5ZbnaeK0DcI+xQEH2nXLyy9UUZANtY6idEilk+EWSWacS7aNhc0TfG+AdR/1nTmKfg+ebDjp9P0wur2cqPYjafm5DQ3negs59+TRE6OMqwmZuuXf1CuoHJbGmdH7zZ8a771V/6LTtJBw2TV/cU58ieqQCqC8Hh8OWlb4rp4n2ddJKNCBykVjyISZ4UQwPzLMS87ijZGu4kZ6w91AzwBgbdoiW9HKi22H8htQKFb2YVAesXg681JUfG0dmdzoFf6kvTyRwKBgQD4t3ISGvNeGuZltmkDkN8dTrqJl0HhloKkR9ZruBTQ54NUqe6AhS1aAZ6Xgr3a0x4XVFiZdNDJHGmNoMvaFhwd/kuNjX0oI+4XQ2wnOvQiolpziww+wPzCvQUhglYGUWrSRh+Rq13nV7jsGuri51W6qLQeBGpMJFzhL2qSuNXYcwKBgQDgZKWR0I+MYWmAbHS+Vtl8EDaenS0pKU5ZP2sm/Y+SZHtnevc5o2tKnIR1ZpJNx2Lo5nb1iqQrFBHKixDDZUr9QLm7xiF19SxqdZ2Pjfisc0EyYIKrS0UnnhtheQgYOE4LKYejv3E2yb20xIqBtu+uApzVUZU9YxgJxeeF7663swKBgGVHVjUXMmjsrUtX1zVzhddKWt1ycYpqHrLE6gSdRiS3UZxlQlClao1PQYRS4fq8o3E+kIGUtvxeDIF2q+wXusVf6uYuanuw/eANPXpQIGAGGq0YPxlCvuqRzAgUKfGSr9wGYA6SaL+u0gebAnD5YMiMsCPVCnANEqFEDLLIf5QPAoGASWfDXgsLGpI3UCKqoiWJKfOn2oVyY5e2oK2FYRDrqakjUXvBamKSU6sWDc3LKJSEP8o8uiBriggbtdV+W11EhcppuAqdoZfdusnVDEQWSFu/xTobJOwPqGEpdd6BDmNE7JPeYmvbHmAqpfrxkHOGvrbuFT6GArnrAC90iLUaw88CgYALFhaOXvKvtqQXGMEuj6CDo+/4Qu8cIm+G9nLS1Z8yTf9dXjJb61REHDR/f6FHwAhOa+y7RtQEuyMG/6o9g8Olo0OQQPsH6RhfZEOy+1MAmr2dkaEYw6OrJNWM/Jozx5gGtww6OsNOTFqmCQqAh6Ae39g/JeoF7i2cr+zeEo5HUQ=="""  # 示例私钥（base64编码）
    public_key = """MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2gJMVE21RJLQAY/1MoAEQBdcMr+RTFmI+fNEW2CEz2QH8y4QmoRdGGj0+q/y0hLfXY9l4oKzNOTo5nZwjXEO/beWh5gRkSMAf7hqbAUJSYQP55JUenxyDiktg8s50q4ZuL20XFpomfTC/umH0RUiiUwTPl8uzCF9vdp9glO4Vq8r29Ecj6h0uj7NBSV08r4s1PpI3N86KvRkpzCtDAD1nADfQtf0PTp9queUODck36FLm1TvXmdIV1vjxWQMKLs0WsFyADk8qz+KENO6aJ25NnoANZudr8pCxemLoXGanE25/r/uEEhLbl4T5kIET2sj3W6LA6n0bJpch+XjlmqNaQIDAQAB"""  # 示例公钥（base64编码）
    content = "This is a message to be signed."
    # RSA签名
    signature = CipherUtils.rsa_sign(private_key, content)
    logger.debug(f"RSA Signature: {signature}")

    # RSA验证
    is_valid = CipherUtils.rsa_verify(public_key, content, signature)
    logger.debug(f"RSA Signature Valid: {is_valid}")
