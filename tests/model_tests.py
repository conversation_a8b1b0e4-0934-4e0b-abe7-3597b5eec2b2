import base64
from datetime import datetime, timedelta
from enum import unique
import os
from typing import ClassV<PERSON>

from loguru import logger
from pydantic import BaseModel, ConfigDict, Field


class MyBaseModel(BaseModel):
    model_config = ConfigDict(str_strip_whitespace=True)
    # ClassVar 不会被当做模型字段
    # 可用来定义常量
    endpoint: ClassVar[str] = '/'
    encrypt: ClassVar[bool] = True


class SubModel(MyBaseModel):
    endpoint: ClassVar[str] = '/sub'

    name: str = Field(..., description="名称")
    age: int = Field(..., description="年龄")

    # def __init__(self, **data):
    #     # 外部传入优先
    #     # if 'endpoint' not in data:
    #     #     data['endpoint'] = "/sub"
    #     # if 'encrypt' not in data:
    #     #     data['encrypt'] = True
    #     super(SubModel, self).__init__(**data)
    #     # self.endpoint = "/sub"


# @pytest.mark.asyncio
def test_mod():
    # 用键（字典形式），适用于用已知的键取对应的元素
    sub = SubModel(name="tony", age=18)
    sub_json = sub.model_dump()
    logger.info(sub_json)
    # 直接用类常量
    assert SubModel.endpoint == "/sub"
    assert SubModel.encrypt is True
    # 对象上使用常量
    assert sub.endpoint == "/sub"
    assert sub.encrypt is True

    assert 'endpoint' not in sub_json
    assert 'encrypt' not in sub_json

    assert sub_json['name'] == "tony"
    assert sub_json['age'] == 18
