import base64
from datetime import datetime, timedelta
from enum import unique
import os

from loguru import logger
from app.config import settings
from app.sdks.triplinkintl.utils import CipherUtils
from commons.cores.base_const import BaseEnum


@unique
class CurrencyISO(BaseEnum):
    """任务状态"""

    CNY = ('156', '人民币元')


# @pytest.mark.asyncio
def test_cover():
    # 用键（字典形式），适用于用已知的键取对应的元素
    currency = CurrencyISO['CNY']
    logger.debug(currency.name)
    logger.debug(currency.value)
    logger.debug(currency.label)

    assert currency.name == 'CNY'
    assert currency.value == '156'
    assert currency.label == '人民币元'
    # 用键（对象形式），适合直接进行if判断的场景
    assert CurrencyISO.CNY.label == '人民币元'
    assert CurrencyISO.CNY.value == '156'
    assert CurrencyISO.CNY.name == 'CNY'

    # 用 value  取元素，适合存储内容为value的情况
    assert CurrencyISO('156').label == '人民币元'
    assert CurrencyISO('156').value == '156'
    assert CurrencyISO('156').name == 'CNY'

    assert CurrencyISO['CNY'] == CurrencyISO.CNY
    assert CurrencyISO('156') == CurrencyISO.CNY
    assert CurrencyISO('156') == CurrencyISO['CNY']
