import base64
from datetime import datetime, timedelta
import os

from loguru import logger
import pytest
from app.config import settings

from app.sdks.airwallex.consts import (
    AirWallexAllowedTransactionCount,
    AirWallexFormFactor,
    AirWallexIssueTo,
    AirWallexTransactionIntervalLimit,
)
from app.sdks.airwallex.model.core.create_card_request import AirWallexCreateCardRequest
from app.sdks.airwallex.model.core.get_card_detail_request import (
    AirWallexGetCardDetailRequest,
    AirWallexGetCardSecretRequest,
)
from app.services.airwallex_service import awx_sdk


@pytest.mark.asyncio
async def test_login():
    token = await awx_sdk.refresh_token()
    assert token


@pytest.mark.asyncio
async def test_create_card():
    token = await awx_sdk.refresh_token()
    request = AirWallexCreateCardRequest(
        # activate_on_issue=True,
        authorization_controls={
            "allowed_transaction_count": AirWallexAllowedTransactionCount.SINGLE.name,
            "transaction_limits": {
                "currency": "USD",
                "limits": [{"amount": 1, "interval": AirWallexTransactionIntervalLimit.PER_TRANSACTION.name}],
                "cash_withdrawal_limits": [
                    {"amount": 10000, "interval": AirWallexTransactionIntervalLimit.PER_TRANSACTION.name}
                ],
            },
        },
        created_by="Demo",
        form_factor=AirWallexFormFactor.VIRTUAL.name,
        issue_to=AirWallexIssueTo.ORGANISATION.name,
        program={"type": "CREDIT", "sub_type": "GOOD_FUNDS_CREDIT"},
        is_personalized=False,
    )
    request._headers = {'Authorization': f'Bearer {token}'}
    result = await awx_sdk.create_card(request)


@pytest.mark.asyncio
async def test_get_card_detail():
    token = await awx_sdk.refresh_token(force=True)
    request = AirWallexGetCardDetailRequest(
        card_id='449a3135-d5d1-41b6-ba12-ad86d1a68fea',
        # card_id='45781104-3894-416e-9872-0880cbc11479'
    )
    request._headers = {'Authorization': f'Bearer {token}'}
    result = await awx_sdk.get_card_detail(request)

    request = AirWallexGetCardSecretRequest(card_id='449a3135-d5d1-41b6-ba12-ad86d1a68fea')
    request._headers = {'Authorization': f'Bearer {token}'}
    result = await awx_sdk.get_card_secret(request)
