#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys

sys.path.append(os.path.abspath(os.path.dirname(os.path.dirname(__file__))))
import pytest

# from sqlalchemy import create_engine

# from sqlalchemy.orm import sessionmaker
from app.config import settings


# @pytest.fixture(scope='module')
# def test_db():
#     """
#     初始化ORM数据库

#     :return:
#     """

#     engine = create_engine(settings.SQLALCHEMY_DATABASE_URI)

#     SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
#     db = SessionLocal()

#     yield db

#     db.close()


# @pytest.fixture(scope='module')
# def fly_scoot_client():
#     from app.clients.flyscoot.flyscoot_client import FlyScootClient

#     tmp_client = FlyScootClient()

#     yield tmp_client
