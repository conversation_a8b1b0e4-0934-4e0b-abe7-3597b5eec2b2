# 基础模块
from typing import Any, List, Optional, Union
from pydantic import BaseModel, Field


class BaseSearchOrderBy(BaseModel):
    prop: str = None
    order: str = None


class BaseSearchIn(BaseModel):
    page: int = 1
    page_size: int = 50
    order_by: Optional[BaseModel] = None


class BaseSearchResult(BaseModel):
    '''查询出参data'''

    total: int
    # page: int
    rows: List[BaseModel]


class BaseApiOut(BaseModel):
    status: Optional[str] = Field(None, description="状态")
    code: Optional[Union[int, str]] = Field(None, description="状态码")
    message: Optional[str] = Field(None, description="信息")
    data: Optional[Any] = Field(None, description="数据")
    client_ip: Optional[str] = Field(None, description="客户端IP")
    request_id: Optional[str] = Field(None, description="请求ID")
