import base64
import os
import random
from typing import Union
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad  # 导入 pad 和 unpad 方法
from Crypto.PublicKey import RSA
from Crypto.Signature import pkcs1_15
from Crypto.Hash import SHA256
from loguru import logger


class CipherUtils:

    @staticmethod
    def generate_random_bytes_key() -> bytes:
        """
        生成16字节的随机密钥（适合AES-128）。
        :return: 16字节随机密钥
        """
        return os.urandom(16)

    @staticmethod
    def generate_random_str_key() -> str:
        """
        生成16字符的随机密钥（适合AES-128）。
        :return: 16字符随机字符串密钥
        """
        # 从常用字符中生成 16 个字符
        chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        return ''.join(random.choice(chars) for _ in range(16))

    @staticmethod
    def validate_key(key: Union[bytes, str]) -> bytes:
        """
        验证并规范化 AES 密钥。
        :param key: 密钥，支持 bytes 或 16 位字符串。
        :return: 规范化后的 16 字节密钥。
        """
        if isinstance(key, str):
            if len(key) != 16:
                raise ValueError("字符串密钥必须是16个字符长")
            key = key.encode()  # 确保字符串密钥被编码为字节
        elif isinstance(key, bytes):
            if len(key) != 16:
                raise ValueError("字节密钥必须是16字节长")
        else:
            raise ValueError("密钥必须是字符串或字节类型")
        return key

    @staticmethod
    def aes_encrypt(key: Union[bytes, str], plaintext: str) -> str:
        """ """
        key = CipherUtils.validate_key(key)
        # 使用AES-128-ECB模式
        cipher = AES.new(key, AES.MODE_CBC, iv=bytes(AES.block_size))
        # 填充数据并加密
        ciphertext = cipher.encrypt(pad(plaintext.encode(), AES.block_size))
        # 返回Base64编码后的密文
        return base64.b64encode(ciphertext).decode('utf-8')

    @staticmethod
    def aes_decrypt(key: Union[bytes, str], ciphertext: str) -> str:
        """
        使用AES算法解密给定的密文。

        :param key: 用于解密的密钥，可以是bytes或16位字符串。
        :param ciphertext: Base64编码的AES加密密文。
        :return: 解密后的明文字符串。
        """
        key = CipherUtils.validate_key(key)
        ciphertext_bytes = base64.b64decode(ciphertext)
        # 使用AES-128-ECB模式，注意ECB模式可能不安全，通常推荐使用更安全的模式如CBC或GCM。
        cipher = AES.new(key, AES.MODE_CBC, iv=bytes(AES.block_size))
        # 解密并去除填充
        decrypted_data = unpad(cipher.decrypt(ciphertext_bytes), AES.block_size)
        logger.debug(f"Decrypted data: {decrypted_data}")  # 打印日志调试信息以便于调试
        # 返回解密后的明文
        return decrypted_data.decode('utf-8')

    @staticmethod
    def rsa_sign(private_key_str: str, content: str) -> str:
        # 载入私钥
        private_key = RSA.import_key(CipherUtils.pem_format(private_key_str, 'PRIVATE KEY'))
        # 使用SHA256哈希
        hash_obj = SHA256.new(content.encode())
        # 生成签名
        signer = pkcs1_15.new(private_key)
        signature = signer.sign(hash_obj)
        # 返回Base64编码后的签名
        return base64.b64encode(signature).decode('utf-8')

    @staticmethod
    def rsa_verify(public_key_str: str, content: str, signature: str) -> bool:
        # 载入公钥
        public_key = RSA.import_key(CipherUtils.pem_format(public_key_str, 'PUBLIC KEY'))
        # 使用SHA256哈希
        hash_obj = SHA256.new(content.encode())
        # 验证签名
        signature_bytes = base64.b64decode(signature)
        try:
            pkcs1_15.new(public_key).verify(hash_obj, signature_bytes)
            return True
        except (ValueError, TypeError):
            return False

    @staticmethod
    def pem_format(key_str: str, label: str) -> str:
        # 将密钥格式化为PEM格式
        return f"-----BEGIN {label}-----\n{key_str}\n-----END {label}-----"


def generate_chinese_phone_number():
    # 三大运营商号段（排除虚拟号段 170/171/162/165/167/168 等）
    carrier_prefixes = {
        "中国移动": [
            '139',
            '138',
            '137',
            '136',
            '135',
            '134',
            '150',
            '151',
            '152',
            '157',
            '158',
            '159',
            '182',
            '183',
            '184',
            '187',
            '188',
            '198',
        ],
        "中国联通": ['130', '131', '132', '155', '156', '185', '186', '145', '176', '166'],
        "中国电信": ['133', '153', '180', '181', '189', '199', '173', '177', '191', '193'],
    }

    # 随机选择运营商
    carrier = random.choice(list(carrier_prefixes.keys()))

    # 随机选择号段
    prefix = random.choice(carrier_prefixes[carrier])

    # 生成后8位
    suffix = ''.join(str(random.randint(0, 9)) for _ in range(8))

    return f"{prefix}{suffix}"


def generate_random_str(length: int = 16):
    allstr = 'abcdefghijklmnopqrstuvwxyz0123456789'
    return ''.join(random.choice(allstr) for _ in range(length))
