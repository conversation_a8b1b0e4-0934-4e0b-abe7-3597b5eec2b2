import asyncio
from datetime import datetime, timezone
import functools
import os
import time
import uuid
import warnings

import curl_cffi
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request, UploadFile, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse, ORJSONResponse
from loguru import logger
import orj<PERSON>

from commons.consts.api_codes import ApiCodes


def retry_on_exceptions(exceptions, max_retries=3, interval=1):
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            retries = 0
            while retries < max_retries:
                try:
                    return await func(*args, **kwargs)
                except tuple(exceptions) as e:
                    logger.info(f"Caught exception: {type(e).__name__}")
                    retries += 1
                    if retries < max_retries:
                        logger.info(f"Retrying in {interval} seconds...")
                        await asyncio.sleep(interval)
                    else:
                        logger.warning("Max retries exceeded. Giving up.")
                        raise e

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except tuple(exceptions) as e:
                    logger.info(f"Caught exception: {type(e).__name__}")
                    retries += 1
                    if retries < max_retries:
                        logger.info(f"Retrying in {interval} seconds...")
                        time.sleep(interval)
                    else:
                        logger.warning("Max retries exceeded. Giving up.")
                        raise e

        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        return wrapper

    return decorator


def retry_on_exceptions2(func):
    # def decorator(func):
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        retries = 0
        max_retries = kwargs.pop('max_retries', 1)
        # 这里默认不含timeout，因为timeout可能导致不幂等问题
        exceptions = kwargs.pop(
            'exceptions', (curl_cffi.requests.exceptions.HTTPError, curl_cffi.requests.exceptions.ConnectionError)
        )
        interval = kwargs.pop('interval', 1)
        pre_call = kwargs.pop('pre_call', None)
        logger.debug(f'retries: {retries}, max_retries: {max_retries}, exceptions: {exceptions}, interval: {interval}')
        while retries < max_retries:
            try:
                return await func(*args, **kwargs)
            except tuple(exceptions) as e:
                logger.info(f"Caught exception: {type(e).__name__}")
                retries += 1
                if retries < max_retries:
                    logger.info(f"Retrying in {interval} seconds...")
                    await asyncio.sleep(interval)
                    # 判断pre_call是否是一个函数，同时区分时同步方法还是异步方法
                    if pre_call and asyncio.iscoroutinefunction(pre_call):
                        await pre_call()
                    elif pre_call and callable(pre_call):
                        pre_call()

                else:
                    logger.warning("Max retries exceeded. Giving up.")
                    raise e

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        retries = 0
        max_retries = kwargs.pop('max_retries', 1)
        # 这里默认不含timeout，因为timeout可能导致不幂等问题
        exceptions = kwargs.pop(
            'exceptions', (curl_cffi.requests.exceptions.HTTPError, curl_cffi.requests.exceptions.ConnectionError)
        )
        interval = kwargs.pop('interval', 1)
        pre_call = kwargs.pop('pre_call', None)
        logger.debug(f'retries: {retries}, max_retries: {max_retries}, exceptions: {exceptions}, interval: {interval}')
        while retries < max_retries:
            try:
                return func(*args, **kwargs)
            except tuple(exceptions) as e:
                logger.info(f"Caught exception: {type(e).__name__}")
                retries += 1
                if retries < max_retries:
                    logger.info(f"Retrying in {interval} seconds...")
                    time.sleep(interval)

                    # 判断pre_call是否是一个函数
                    if pre_call and callable(pre_call):
                        pre_call()
                else:
                    logger.warning("Max retries exceeded. Giving up.")
                    raise e

    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    return wrapper


# return decorator


async def custom_request_logger(request: Request, call_next):
    from commons.extensions.logger_extras import log_uid

    request_id = request.headers.get('X-Request-ID', str(uuid.uuid4()))  # 生成唯一的请求ID

    start_time = time.time()
    # logger.info(f"Request start {request_id} [{request.method}] {request.url.path}")
    # 捕获请求体
    # 捕获请求体并处理文件
    request_body_str = None
    if request.headers.get('content-type', '').startswith('multipart/form-data'):
        # 处理 multipart/form-data, 假设其中可能包含文件
        # form = await request.form()
        # file_names = [file.filename for file in form.values() if isinstance(file, UploadFile)]
        # request_body_str = f"Files: {file_names}" if file_names else "No files"
        request_body_str = '文件上传'
    else:
        # 处理普通请求体
        request_body = await request.body()
        request_body_str = request_body.decode('utf-8') if request_body else None
        if request_body_str:
            try:
                request_body_json = orjson.loads(request_body_str)
                request_id = request_body_json.get('request_id', request_id)
            except orjson.JSONDecodeError:
                pass

    log_uid.set(request_id)  # 给日志增加唯一标识
    request.state.request_id = request_id

    try:

        response = await call_next(request)
        # 读取响应体的内容
        logger.debug(response.headers)
        if 'filename=' in response.headers.get('content-disposition', ''):
            response_body_str = '下载文件'
        else:
            response_body = b""
            async for chunk in response.body_iterator:
                response_body += chunk
            response_body_str = response_body.decode('utf-8')

            # 创建一个异步生成器来返回响应体
            async def response_body_generator():
                yield response_body

            # 重新分配响应体为异步生成器
            response.body_iterator = response_body_generator()

    except Exception as e:
        if len(e.args) > 1:
            error_response = ApiCodes.generate_result(code=e.args[0], msg=e.args[1], status='failed')
            logger.warning(e)
        else:
            error_response = ApiCodes.UNKNOWN.generate_api_result(data='', ext_msg=str(e))
            logger.exception(e)
        response_body_str = orjson.dumps(error_response).decode('utf-8')
        response = ORJSONResponse(content=error_response, status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
    finally:
        end_time = time.time()
        # logger.info(f"Response end: ({process_time}) {response.status_code} {response_body_str}")
        try:
            resp_json = orjson.loads(response_body_str)
            logger.bind(write_tag="api_es_log").info(
                '',
                api_type="receive",
                api_path=request.url.path,
                request=request_body_str,
                response=response_body_str,
                status=resp_json.get('status', ''),
                code=resp_json.get('code', -1),
                message=resp_json.get('message', ''),
                cost_time=end_time - start_time,
                start_time=datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S'),
                end_time=datetime.fromtimestamp(end_time).strftime('%Y-%m-%d %H:%M:%S'),
            )
        except:
            pass

    # # 在请求处理完成后记录详细的日志，包括请求ID、路径、方法和耗时
    # log_details = {
    #     "request_id": request_id,
    #     "path": request.url.path,
    #     "method": request.method,
    #     "request": request_body_str,
    #     "response": response_body_str,
    #     "request_time": round(process_time, 8),
    # }
    # # logger.info(f"Request end {request_id} {round(process_time, 8)}: {response.status_code} {log_details}")
    # logger.info(f"Request end {log_details}")
    # 将请求ID添加到响应头中，可选
    response.headers['X-Request-ID'] = request_id

    return response


async def custom_request_validation(request: Request, exc: RequestValidationError):

    error_response = ApiCodes.PARAMS_ERROR.generate_api_result(data='', ext_msg=str(exc))

    # 返回自定义的JSON响应
    return JSONResponse(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, content=error_response)


async def custom_exception_handler(request: Request, exc: Exception):
    # logger.error(f"Exception: {exc}")
    if len(exc.args) > 1:
        error_response = ApiCodes.generate_result(code=exc.args[0], msg=exc.args[1], status='failed')
    else:
        error_response = ApiCodes.UNKNOWN.generate_api_result(data='', ext_msg=str(exc))

    # 返回自定义的JSON响应
    return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content=error_response)


def deprecated(reason):
    """
    :param reason: the reason why the function is deprecated
    :type reason: str
    """

    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            warnings.warn(f"{func.__name__} is deprecated: {reason}", category=DeprecationWarning, stacklevel=2)
            return await func(*args, **kwargs)

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            warnings.warn(f"{func.__name__} is deprecated: {reason}", category=DeprecationWarning, stacklevel=2)
            return func(*args, **kwargs)

        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        return wrapper

    return decorator
