import asyncio
import contextvars
from datetime import datetime, timezone
import os
import threading
import traceback

import orjson

# 定义一个全局的上下文变量，用于存储额外信息
log_extra_unique_id = contextvars.ContextVar("log_extra_request_id", default=None)
log_server_name = contextvars.ContextVar("log_server_name", default=None)


def get_uid_prefix():
    uid_prefix = []
    try:
        # 取得进程ID
        pid = os.getpid()

        # 初始化协程或线程名称变量
        ctn = None
        uid_prefix.append(str(pid))
        # 判断是否运行在协程环境
        if asyncio.current_task():
            ctn = asyncio.current_task().get_name()
        else:
            # 使用线程名称
            ctn = threading.current_thread().name

        # 生成UID前缀
        if ctn:
            uid_prefix.append(str(ctn))
    except:
        pass
    if uid_prefix:
        return '-'.join(uid_prefix)
    return ''


class MaskUniqueID:
    """
    对log_extra_unique_id进行封装
    因为logger.configure()中的extra参数无法传递contextvars.ContextVar
    """

    def set(self, value):
        uid = get_uid_prefix()
        if value:
            uid = f'{uid}-{uid}'
        log_extra_unique_id.set(value)

    def __str__(self) -> str:
        # 在进行logger.configure时，此处可以动态返回log_extra_unique_id
        if not log_extra_unique_id.get():
            return ''
        return str(log_extra_unique_id.get())


log_uid = MaskUniqueID()
log_uid.set('')

DEFAULT_LOG_FORMAT = (
    '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}.{function}:{line} | uid:{extra[unique_id]} | {message}'
)

PROJECT_PATH: str = os.path.abspath(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))


def process_exception(exception):
    """
    如果 traceback 中不包含项目路径，则仅保留前两层 traceback
    """

    return {
        "type": None if exception.type is None else exception.type.__name__,
        "value": exception.value,
        # 只打印本项目代码的 traceback
        "traceback": traceback.format_list(
            [tb for tb in traceback.extract_tb(exception.traceback) if PROJECT_PATH in str(tb)]
        ),
    }


def es_sink(message):
    record = message.record.copy()  # 复制日志记录
    text = getattr(message, 'text', '')
    exception = record["exception"]

    if exception is not None:
        exception = process_exception(exception)
        # exception = {
        #     "type": None if exception.type is None else exception.type.__name__,
        #     "value": exception.value,
        #     "traceback": bool(exception.traceback),
        # }

    serializable = {}
    if record.get('extra'):
        serializable.update(record['extra'])
    if record.get('extra') and record['extra'].get('start_time'):
        t = record['extra']['start_time']
        if 'T' in t:
            t = datetime.strptime(t, '%Y-%m-%dT%H:%M:%S')
        else:
            t = datetime.strptime(t, '%Y-%m-%d %H:%M:%S')
        ts = t.astimezone(timezone.utc).isoformat()
    else:
        t = record.get("time")
        if isinstance(t, datetime):
            # 将已有 datetime 转为 UTC，并格式化为 ISO8601 字符串
            ts = t.astimezone(timezone.utc).isoformat()
        else:
            ts = datetime.fromtimestamp(t, tz=timezone.utc).isoformat()

    serializable.update({"exception": exception, "@timestamp": ts})

    print(orjson.dumps(serializable, option=orjson.OPT_APPEND_NEWLINE, default=str).decode("utf-8"))
    # return None


def es_api_sink(message):
    record = message.record.copy()  # 复制日志记录
    exception = record["exception"]

    if exception is not None:
        exception = process_exception(exception)
        # exception = {
        #     "type": None if exception.type is None else exception.type.__name__,
        #     "value": exception.value,
        #     "traceback": bool(exception.traceback),
        # }

    # if record.get('extra'):
    #     serializable.update(record['extra'])
    if record.get('extra') and record['extra'].get('start_time'):
        t = record['extra']['start_time']
        if 'T' in t:
            t = datetime.strptime(t, '%Y-%m-%dT%H:%M:%S')
        else:
            t = datetime.strptime(t, '%Y-%m-%d %H:%M:%S')
        ts = t.astimezone(timezone.utc).isoformat()
    else:
        t = record.get("time")
        if isinstance(t, datetime):
            # 将已有 datetime 转为 UTC，并格式化为 ISO8601 字符串
            ts = t.astimezone(timezone.utc).isoformat()
        else:
            ts = datetime.fromtimestamp(t, tz=timezone.utc).isoformat()

    serializable = {
        "server_name": record["extra"].get("server_name", str(log_server_name.get())),
        "unique_id": record["extra"].get("unique_id", str(log_uid)),
        "api_type": record["extra"].get('api_type', ''),
        "api_path": record["extra"].get('api_path', ''),
        "request": record["extra"].get('request', ''),
        "response": record["extra"].get('response', ''),
        "status": record["extra"].get('status', ''),
        "code": record["extra"].get('code', -1),
        "message": record["extra"].get('message', ''),
        "cost_time": record["extra"].get('cost_time', 0),
        "exception": exception,
        "@timestamp": ts,
    }
    if record.get('extra') and record['extra'].get('start_time'):
        serializable.update({'start_time': record['extra']['start_time']})
    if record.get('extra') and record['extra'].get('end_time'):
        serializable.update({'end_time': record['extra']['end_time']})

    print(orjson.dumps(serializable, option=orjson.OPT_APPEND_NEWLINE, default=str).decode("utf-8"))
    # return None
