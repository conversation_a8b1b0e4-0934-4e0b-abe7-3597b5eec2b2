import aioredis
from loguru import logger
import redis
import time
import asyncio

from commons.decorators import deprecated


async def get_redis(redis_url, **kwargs):
    ext_options = kwargs
    if 'decode_responses' not in ext_options:
        ext_options['decode_responses'] = True
    return await aioredis.from_url(redis_url, **kwargs)


@deprecated(
    reason='设计不合理，全局对象with模式在高并发时可能导致对象关闭，请根据场景使用分别使用AsyncRedisPool和SyncRedisPool。'
)
class RedisPool:
    def __init__(self, redis_url, **kwargs):
        self.redis_pool = None
        self.redis_url = redis_url
        self.ext_options = kwargs
        if 'decode_responses' not in self.ext_options:
            self.ext_options['decode_responses'] = True
        self.sync_redis = None

    async def __aenter__(self):
        self.redis_pool = await aioredis.from_url(self.redis_url, **self.ext_options)
        return self.redis_pool

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.redis_pool.close()
        if exc_type is not None:
            raise exc_val

    def __enter__(self):
        self.sync_redis = redis.Redis.from_url(self.redis_url, **self.ext_options)
        return self.sync_redis

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.sync_redis.close()
        if exc_type is not None:
            raise exc_val


def get_redis_sync(redis_url, **kwargs):
    ext_options = kwargs
    if 'decode_responses' not in ext_options:
        ext_options['decode_responses'] = True
    return redis.Redis.from_url(redis_url, **kwargs)


class SyncRedisPool:
    def __init__(self, redis_url, **kwargs):
        self.redis_url = redis_url
        self.redis_options = kwargs
        self.redis_client = None

    def __enter__(self):
        self.redis_client = redis.Redis.from_url(self.redis_url, **self.redis_options)
        return self.redis_client

    def __exit__(self, exc_type, exc_val, exc_tb):
        try:
            self.redis_client.close()
        except Exception as e:
            # Log the error if necessary
            logger.error(f"Error while closing sync pool: {e}")
        if exc_type is not None:
            raise exc_val


class AsyncRedisPool:
    def __init__(self, redis_url, **kwargs):
        self.redis_url = redis_url
        self.redis_options = kwargs
        self.redis_client = None

    async def __aenter__(self):
        self.redis_client = await aioredis.from_url(self.redis_url, **self.redis_options)
        return self.redis_client

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        try:
            await self.redis_client.close()
        except Exception as e:
            # Log the error if necessary
            logger.error(f"Error while closing async pool: {e}")
        if exc_type is not None:
            raise exc_val
