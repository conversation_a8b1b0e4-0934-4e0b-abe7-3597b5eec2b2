from typing import Any, ClassVar, Dict, List, Optional
from loguru import logger
from pydantic import BaseModel, Field, field_validator, model_validator

from commons.fastapi.schemas.common_schemas import BaseApiOut
from commons.sdks.base import BaseRequestModel
from commons.sdks.pay_center.common_model import CardInfo, CardInfoSecret


class GetCardListRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/pay_center/public/pay_card/list'

    order_no: str = Field(..., description="订单号")


class GetCardListResponse(BaseApiOut):
    # 密文返回，用于页面接口
    data: List[CardInfoSecret] = Field(default_factory=list, description="支付卡信息")
