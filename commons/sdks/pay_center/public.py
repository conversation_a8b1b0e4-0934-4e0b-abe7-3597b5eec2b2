from typing import Any, ClassVar, Dict, Optional
from loguru import logger
from pydantic import BaseModel, Field, field_validator, model_validator

from commons.fastapi.schemas.common_schemas import BaseApiOut
from commons.sdks.base import BaseRequestModel
from commons.sdks.pay_center.common_model import CardInfo, CardInfoSecret


class PayCenterPublicCreateCardRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/pay_center/public/pay_card/create'

    order_no: str = Field(..., description="订单号")
    airline_code: str = Field(..., description="航空公司代码")
    flight_dep_time: str = Field(..., description="航班出发时间 YYYY-MM-DD HH:mm:ss")
    total_price: float = Field(..., description="总价")
    currency_code: str = Field(..., description="币种")
    channel_type: Optional[str] = Field('web', description="出票渠道类型: web 官网; agent 代理商")
    supplier_code: Optional[str] = Field(None, description="供应商代码")

    @field_validator('airline_code', 'currency_code', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if v and isinstance(v, str):
            return v.upper()
        return v


class PayCenterPublicCreateCardResponse(BaseApiOut):
    # 明文返回，用于public接口
    data: Optional[CardInfo] = Field(default=None, description="支付卡信息")


class PayCenterPublicCreateCardSecretResponse(BaseApiOut):
    # 密文返回，用于页面接口
    data: Optional[CardInfoSecret] = Field(default=None, description="支付卡信息")
