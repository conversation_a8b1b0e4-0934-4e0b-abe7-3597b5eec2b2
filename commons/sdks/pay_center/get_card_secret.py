from typing import Any, ClassVar, Dict, List, Optional
from loguru import logger
from pydantic import Field

from commons.fastapi.schemas.common_schemas import BaseApiOut
from commons.sdks.base import BaseRequestModel
from commons.sdks.pay_center.common_model import CardInfo


class GetCardSecretRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/pay_center/public/pay_card/secret'

    id: int = Field(..., description="主键ID")


class GetCardSecretResponse(BaseApiOut):
    # 密文返回，用于页面接口
    data: CardInfo = Field(default_factory=list, description="支付卡信息")
