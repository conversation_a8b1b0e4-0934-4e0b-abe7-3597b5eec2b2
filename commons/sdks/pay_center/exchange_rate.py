from typing import Any, ClassVar, Dict, Optional
from loguru import logger
from pydantic import BaseModel, Field, field_validator, model_validator

from commons.fastapi.schemas.common_schemas import BaseApiOut
from commons.sdks.base import BaseRequestModel


class GetExchangeRateRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/pay_center/public/exchange/rate'

    from_currency: str = Field(..., description="原币种")
    to_currency: str = Field(..., description="目标币种")
    amount: float = Field(None, description="原币金额")
    use_cache: Optional[bool] = Field(True, description="使用缓存数据进")
    service_type: Optional[str] = Field('unionpayintl', description="服务类型,unionpayintl:银联,chinamoney:中国银行")

    @field_validator('from_currency', 'to_currency', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        return v


class ExchangeRateResult(GetExchangeRateRequest):

    rate: float = Field(..., description="汇率")


class GetExchangeRateResponse(BaseApiOut):
    data: ExchangeRateResult
