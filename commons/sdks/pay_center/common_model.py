from typing import Any, Dict
from loguru import logger
from pydantic import BaseModel, Field, field_validator, model_validator

from commons.sdks.base import GLOBAL_MODEL_CONFIG


class CardInfo(BaseModel):
    id: int = Field(..., description="主键ID")
    order_no: str = Field(..., description="订单号")
    card_no: str = Field(..., description="卡号")
    card_expiration_date: str = Field(..., description="卡有效期")
    cvv2: str = Field(..., description="卡CVV2")
    card_type: str = Field(..., description="卡类型")
    card_label: str = Field(..., description="卡组织")
    currency_code: str = Field(..., description="币种")
    status: str = Field(..., description="状态")
    created: str = Field(..., description="创建时间")
    updated: str = Field(..., description="更新时间")


class CardInfoSecret(CardInfo):
    @field_validator('card_no', 'card_expiration_date', 'cvv2', mode='before')
    @classmethod
    def encrypt(cls, v: Any):
        if len(v) > 10:
            prefix = v[:6]
            suffix = v[-4:]
            return prefix + '*' * (len(v) - 10) + suffix
        if len(v) > 4:
            return '*' * len(v[:-4]) + v[-4:]
        return '*' * len(v)
