from typing import Any, ClassVar, Dict, Optional, Union

from pydantic import Field
from commons.sdks.base import BaseRequestModel


class HelperServiceTlsForwardRequest(BaseRequestModel):
    """
    淘宝临时订单拉取请求
    """

    endpoint: ClassVar[str] = '/api/v1/helper_service/tls/forward'

    timeout: Optional[Union[int, float]] = Field(default=60, description="请求超时时间")
    tls_type: Optional[str] = Field(default='curl_cffi', description="TLS类型，可选值：'curl_cffi'")
    method: str = Field(..., description="请求方法")
    url: str = Field(..., description="请求URL")
    headers: Dict[str, Any] = Field(None, description="请求头，Map字典")
    cookies: Dict[str, Any] = Field(None, description="cookies，Map字典")
    data: Union[str, Dict[str, Any]] = Field(None, description="请求体自己拼接为字符串")
    allow_redirects: bool = Field(None, description="是否允许重定向")
    proxy_str: str = Field(None, description="代理字符串, 格式：'协议://username:password@host:port'")
    verify: bool = Field(False, description="是否验证SSL证书")
