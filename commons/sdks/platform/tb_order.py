from typing import Any, ClassVar, Dict, List, Optional
from loguru import logger
from pydantic import BaseModel, Field, field_validator

from commons.fastapi.schemas.common_schemas import BaseApiOut
from commons.sdks.base import BaseRequestModel
from commons.sdks.common_model import ContactForOrder, FlightForOrder, PassengerForOrder
from commons.sdks.pay_center.common_model import CardInfo


class TempOrderInfo(BaseModel):
    """
    淘宝临时订单拉取信息
    """

    order_no: str = Field(..., description="订单号")
    mock_pnr: str = Field(..., description="mock PNR")

    flight: FlightForOrder

    contact: ContactForOrder
    passengers: List[PassengerForOrder]

    extra: Optional[Dict[str, Any]] = Field(default_factory=dict, description="扩展字段")


class TaoBaoTempOrderPullRequest(BaseRequestModel):
    """
    淘宝临时订单拉取请求
    """

    endpoint: ClassVar[str] = '/api/v1/platform/tb/public/tmp_order/pull'

    mock_pnr: str = Field(..., description="mock PNR")


class TaoBaoTempOrderPullResponse(BaseApiOut):
    """
    淘宝临时订单拉取响应
    """

    data: TempOrderInfo
