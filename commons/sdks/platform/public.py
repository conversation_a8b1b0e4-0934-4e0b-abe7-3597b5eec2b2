from typing import Any, ClassVar, Dict, List, Optional
from loguru import logger
from pydantic import BaseModel, Field, field_validator

from commons.fastapi.schemas.common_schemas import BaseApiOut
from commons.sdks.base import BaseRequestModel
from commons.sdks.common_model import ContactForOrder, FlightForOrder, PassengerForOrder
from commons.sdks.pay_center.common_model import CardInfo


class PlatformPublicTicketDetailRequest(BaseRequestModel):
    """
    平台出票详情查询接口，入参
    """

    endpoint: ClassVar[str] = '/api/v1/platform/public/ticket/detail'
    order_no: str = Field(..., description="订单号")
    ota_code: str = Field(..., description="OTA代码")


class OrderInfo(BaseModel):
    """
    订单信息
    """

    order_no: str = Field(..., description="订单号")
    ota_code: str = Field(..., description="OTA代码")
    verify_status: int = Field(..., description="验真状态 0 正在验真 1 验真通过 2 验真失败")


class PlatformPublicTicketDetailResponse(BaseApiOut):
    """
    平台出票详情查询接口，出参
    """

    data: Optional[OrderInfo] = Field(None, description="订单详情")


class PlatformPublicPriceCompareRequest(BaseRequestModel):
    """
    平台价格对比接口，入参
    """

    endpoint: ClassVar[str] = '/api/v1/platform/public/price/compare'

    ota_code: str = Field(..., description="OTA代码")
    airline_code: str = Field(..., description="航空公司代码")
    dep_city_code: str = Field(..., description="出发城市代码")
    arr_city_code: str = Field(..., description="到达城市代码")
    dep_date: str = Field(..., description="出发日期")
    product_types: Optional[List[int]] = Field(
        [0],
        description="产品类型: 1、国内：0，普通；1，多人成行；2，学生专享；3，青年专享；4，老年专享；5，地区专享；6，会员；9，家庭套票；10，学生认证专享；11，年龄票。 2、国际：0，普通；1，多人成行；7，留学生；8，国籍；11，年龄票。接口默认：国际+0普通",
    )
    sale_mode_codes: Optional[List[int]] = Field(
        [0, 7],
        description="销售方式: 1、国内：0，普通；1，旅行销售套餐1；2，旅行销售套餐2；3，旅行销售套餐3；4，返现-航司运价；5，返现-销售方包装；6，花呗卖家版。 2、国际：0，普通；6，花呗卖家版；7，金牌；8，极速；9，限时免费退；10，延时出票；11，延时出票二档；12，延时出票三档。接口默认：国际+0普通+7金牌",
    )
    baggage_type: Optional[int] = Field(
        None, description="行李额: 1，(0,15)kg；2，[15,30)kg；3，[30,+∞)kg；4，0kg；不填默认为全部。"
    )

    @field_validator('airline_code', 'dep_city_code', 'arr_city_code', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if v and isinstance(v, str):
            return v.upper()
        return v

    @field_validator('ota_code', mode='before')
    @classmethod
    def to_lower(cls, v: Any):
        if v and isinstance(v, str):
            return v.lower()
        return v


class PriceCompareItem(BaseModel):
    """
    价格对比项
    """

    channel_code: str = Field(..., description="渠道代码")
    airline_code: str = Field(..., description="航空公司代码")
    flight_date: str = Field(..., description="航班日期")
    dep_city_code: str = Field(..., description="出发城市代码")
    arr_city_code: str = Field(..., description="到达城市代码")
    cabin_code: str = Field(..., description="舱位代码")
    cabin_level: str = Field(..., description="舱位等级")
    flight_no: str = Field(..., description="航班号")
    dep_airport_code: str = Field(..., description="出发机场代码")
    arr_airport_code: str = Field(..., description="到达机场代码")
    adult_base: float = Field(..., description="成人基础价")
    adult_tax: float = Field(..., description="成人税费")
    adult_total: float = Field(..., description="成人总价")
    self_base: float = Field(..., description="自身基础价")
    self_tax: float = Field(..., description="自身税费")
    self_total: float = Field(..., description="自身总价")
    baggage_min_weight: float = Field(..., description="最小行李额")
    baggage_max_weight: float = Field(..., description="最大行李额")

    @field_validator(
        'airline_code',
        'dep_city_code',
        'arr_city_code',
        'dep_airport_code',
        'arr_airport_code',
        'cabin_code',
        'cabin_level',
        'flight_no',
        mode='before',
    )
    @classmethod
    def to_upper(cls, v: Any):
        if v and isinstance(v, str):
            return v.upper()
        return v


class PlatformPublicPriceCompareResponse(BaseApiOut):
    """
    平台价格对比接口，出参
    """

    data: Optional[List[PriceCompareItem]] = Field(None, description="价格对比结果")
