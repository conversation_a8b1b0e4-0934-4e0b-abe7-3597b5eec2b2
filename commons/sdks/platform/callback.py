from typing import Any, ClassVar, Dict, List, Optional
from loguru import logger
from pydantic import BaseModel, Field, field_validator

from commons.fastapi.schemas.common_schemas import BaseApiOut
from commons.sdks.base import BaseRequestModel
from commons.sdks.common_model import ContactForOrder, FlightForOrder, PassengerForOrder
from commons.sdks.pay_center.common_model import CardInfo


class TicketResultSegment(BaseModel):
    dep_city_code: str = Field(..., description="出发城市代码")
    arr_city_code: str = Field(..., description="到达城市代码")


class TicketResultPassenger(BaseModel):
    name: str = Field(..., description="姓名")
    ticket_nos: List[str] = Field(..., description="票号列表")
    pnr: str = Field(..., description="PNR")
    segments: List[TicketResultSegment] = Field(..., description="航段列表")


class PlatformCallbackTicketResultRequest(BaseRequestModel):
    """
    淘宝出票结果回传
    """

    endpoint: ClassVar[str] = '/api/v1/platform/callback/ticket/result'

    order_no: str = Field(..., description="订单号")
    ota_code: str = Field(..., description="OTA代码")
    passengers: List[TicketResultPassenger] = Field(..., description="乘机人列表")
