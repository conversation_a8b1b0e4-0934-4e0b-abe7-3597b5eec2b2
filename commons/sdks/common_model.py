from typing import Any, Optional
from pydantic import BaseModel, Field, field_validator

from commons.sdks.base import GLOBAL_MODEL_CONFIG


class FlightForOrder(BaseModel):
    airline_code: str = Field(..., description="航司")
    dep_airport_code: str = Field(..., description="出发机场")
    arr_airport_code: str = Field(..., description="到达机场")
    flight_no: str = Field(..., description="航班号")
    currency_code: str = Field(..., description="币种")
    dep_date: str = Field(..., description="出发日期")
    return_date: Optional[str] = Field(None, description="返程日期")
    cabin_class: str = Field(..., description="舱位等级")
    src_adult_base: float = Field(..., description="成人单价")
    src_adult_tax: float = Field(..., description="成人税费")

    @field_validator('airline_code', 'dep_airport_code', 'arr_airport_code', 'currency_code', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        return v


class PassengerForOrder(BaseModel):
    name: str = Field(None, description="姓名")
    last_name: str = Field(..., description="姓")
    first_name: str = Field(..., description="名")
    birthday: str = Field(..., description="出生日期")
    sex: str = Field(..., description="性别")
    passenger_type: str = Field(..., description="乘客类型")
    country: str = Field(..., description="国籍")
    card_no: str = Field(..., description="证件号码")
    card_valid_date: str = Field(..., description="证件有效期")
    card_country: str = Field(..., description="证件国家")

    @field_validator('country', 'card_country', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        return v


class ContactForOrder(BaseModel):
    last_name: str = Field(..., description="姓")
    first_name: str = Field(..., description="名")
    mobile: str = Field(..., description="手机号")
    email: str = Field(..., description="email 邮箱地址")


class ErrorInfo(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    code: int = Field(..., description="错误代码或成功标识")
    message: str = Field(..., description="错误或成功信息")
