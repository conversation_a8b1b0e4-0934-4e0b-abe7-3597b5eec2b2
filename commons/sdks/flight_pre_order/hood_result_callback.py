from datetime import datetime
from typing import Any, ClassVar, Dict, List, Optional, Union

from pydantic import BaseModel, Field, field_validator
from commons.sdks.base import GLOBAL_MODEL_CONFIG, BaseRequestModel
from commons.sdks.common_model import ErrorInfo


class HoodResult(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    hold_success: bool = Field(..., description="是否成功")
    seat_num: int = Field(..., description="占座数量")
    cabin_code: str = Field(..., description="舱位代码")
    cabin_level: str = Field(..., description="舱位等级")
    adult_base: Union[int, float] = Field(..., description="成人票价")
    adult_tax: Union[int, float] = Field(..., description="成人税费")
    max_adult_base: Union[int, float] = Field(None, description="最高票价（下个高价舱位）")
    max_adult_tax: Union[int, float] = Field(None, description="最高税费（下个高价舱位）")
    currency_code: str = Field(..., description="币种")
    dep_airport_code: str = Field(..., description="出发机场")
    arr_airport_code: str = Field(..., description="到达机场")
    dep_date: str = Field(..., description="出发日期")
    flight_no: str = Field(..., description="航班号")
    expired_time: str = Field(None, description="过期时间")
    flight_info: Optional[Dict[str, Any]] = Field(default=None, description="航班信息")
    extra: Optional[Dict[str, Any]] = Field(default_factory=dict, description="扩展字段")

    @field_validator(
        'dep_airport_code', 'arr_airport_code', 'flight_no', 'cabin_code', 'cabin_level', 'currency_code', mode='before'
    )
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        return v


class HoodResultCallbackRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_pre_order/crawler/callback/hood/result'

    task_info: Dict[str, Any] = Field(..., description="任务信息")
    data: Optional[HoodResult] = Field(default=None, description="结果数据")
    error: ErrorInfo = Field(..., description="错误信息")
