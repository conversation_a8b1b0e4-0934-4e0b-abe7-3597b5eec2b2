from typing import Any, ClassVar, Dict, List, Optional
from pydantic import BaseModel, Field, field_validator
from pydantic.main import IncEx
from commons.sdks.base import BaseRequestModel
from commons.sdks.crawler.normal_book import NormalBookRequest


# class Flight(BaseModel):
#     airline_code: str = Field(..., description="航司")
#     dep_airport_code: str = Field(..., description="出发机场")
#     arr_airport_code: str = Field(..., description="到达机场")
#     flight_no: str = Field(..., description="航班号")
#     currency_code: str = Field(..., description="币种")
#     dep_date: str = Field(..., description="出发日期")
#     return_date: Optional[str] = Field(None, description="返程日期")
#     cabin_class: str = Field(..., description="舱位等级")
#     src_adult_base: float = Field(..., description="成人单价")
#     src_adult_tax: float = Field(..., description="成人税费")

#     @field_validator('airline_code', 'dep_airport_code', 'arr_airport_code', 'currency_code', mode='before')
#     @classmethod
#     def to_upper(cls, v: Any):
#         if isinstance(v, str):
#             return v.upper()
#         return v


# class Product(BaseModel):
#     fare_type: str = Field(..., description="票价类型，例如 normal")
#     fare_key: Optional[str] = Field(None, description="票价键")


# class Contact(BaseModel):
#     last_name: str = Field(..., description="姓")
#     first_name: str = Field(..., description="名")
#     mobile: str = Field(..., description="手机号")
#     email: str = Field(..., description="email 邮箱地址")


# class Passenger(BaseModel):
#     name: str = Field(None, description="姓名")
#     last_name: str = Field(..., description="姓")
#     first_name: str = Field(..., description="名")
#     birthday: str = Field(..., description="生日")
#     sex: str = Field(..., description="性别")
#     passenger_type: str = Field(..., description="乘客类型")
#     country: str = Field(..., description="乘客国籍")
#     card_no: str = Field(..., description="护照号")
#     card_valid_date: str = Field(..., description="护照到期")
#     card_country: str = Field(..., description="护照签发国")


# class Service(BaseModel):
#     name: str = Field(..., description="附加服务")
#     code: str = Field(..., description="编码")


class CreateHoodBookRequest(NormalBookRequest):
    endpoint: ClassVar[str] = '/api/v1/flight_pre_order/public/hood/book/create'

    crawler_code: Optional[str] = Field(None, description="爬虫模块标识码，用小写的航司二字码")
    # request_exclude: ClassVar[IncEx] = set(['crawler_code'])
    expire_time: str = Field(..., description="过期时间")

    # crawler_code: str = Field(..., description="爬虫模块标识码，用小写的航司二字码")

    # order_no: str = Field(..., description="订单号")
    # real_order: bool = Field(..., description="是否真实订单 true/false")
    # book_start_check: str = Field(..., description="订座前检查url")
    # pay_start_check: str = Field(..., description="订座前检查url true/false")
    # callback_url: str = Field(..., description="回调 URL")
    # pay_callback_url: str = Field(..., description="支付回调 URL")

    # flight: Flight
    # product: Product
    # contact: Contact
    # passengers: List[Passenger]
    # services: List[Service]
    # extra: Optional[Dict[str, Any]] = Field(default_factory=dict, description="扩展字段")

    # @field_validator('crawler_code', mode='before')
    # @classmethod
    # def to_lower(cls, v: Any):
    #     if isinstance(v, str):
    #         return v.lower()
    #     return v
