from typing import Any, ClassVar, Optional
from pydantic import BaseModel, Field, field_validator
from commons.sdks.base import BaseRequestModel


#


class CreateOnceRuleRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_pre_order/public/once_rule/create'

    airline_code: Optional[str] = Field(..., description="航司(站点)ID，外键")
    batch_tags: Optional[str] = Field(..., description="批次标签")
    dep_airport_code: Optional[str] = Field(..., description="出发机场三字码")
    arr_airport_code: Optional[str] = Field(..., description="到达机场三字码")
    flight_date: Optional[str] = Field(..., description="航班日期")
    flight_no: Optional[str] = Field(..., description="航班号")
    # hood_type: Optional[str] = Field(HoodType.ONCE.value, description="压位类型")
    hood_limit: Optional[int] = Field(..., description="单个航班压单上限，注意这里要对比的是单个fare，0表示不限制")
    currency_code: Optional[str] = Field(..., description="币种")

    @field_validator(
        'airline_code', 'dep_airport_code', 'arr_airport_code', 'flight_no', 'currency_code', mode='before'
    )
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        return v
