from datetime import datetime
from typing import Any, ClassVar, Dict, List, Optional, Union

from pydantic import BaseModel, Field
from commons.sdks.base import GLOBAL_MODEL_CONFIG, BaseRequestModel
from commons.sdks.common_model import ErrorInfo


class PayResult(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    order_no: str = Field(..., description="订单号")
    trade_no: str = Field(..., description="交易单号")
    pay_account: str = Field(..., description="支付账号")
    pay_amount: float = Field(..., description="支付金额")
    currency_code: str = Field(..., description="币种")
    pnr: str = Field(None, description="PNR")
    ticket_email: str = Field(None, description="出票账号（邮箱）")


class PayResultCallbackRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_pre_order/crawler/callback/pay/result'

    task_info: Dict[str, Any] = Field(..., description="任务信息")
    data: Optional[PayResult] = Field(None, description="结果数据")
    error: ErrorInfo = Field(..., description="错误信息")
