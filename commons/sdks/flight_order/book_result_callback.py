from datetime import datetime
from typing import Any, ClassVar, Dict, List, Optional, Union

from pydantic import BaseModel, Field, field_validator
from commons.sdks.base import GLOBAL_MODEL_CONFIG, BaseRequestModel
from commons.sdks.common_model import ErrorInfo


class Passenger(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    last_name: str = Field(..., description="姓")
    first_name: str = Field(..., description="名")
    card_no: str = Field(..., description="护照号")


class BookResult(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    # 订单
    order_no: str = Field(..., description="订单号")
    fare_key: str = Field(..., description="票价唯一标识")
    # 航班
    dep_airport_code: str = Field(..., description="出发机场三字码")
    arr_airport_code: str = Field(..., description="到达机场三字码")
    flight_no: str = Field(..., description="航班号")
    dep_date: str = Field(..., description="出发日期")
    # 结果
    pnr: str = Field(..., description="PNR")
    adult_price: Union[int, float] = Field(..., description="成人价")
    adult_tax: Union[int, float] = Field(..., description="成人税费")
    total_price: Union[int, float] = Field(..., description="总价")

    mobile_no: str = Field(..., description="手机号")
    email: str = Field(..., description="邮箱")
    currency_code: str = Field(..., description="币种")

    passenger_num: int = Field(..., description="乘客人数")
    passengers: List[Passenger] = Field(..., description="乘客信息")
    # 支付信息
    pay_limit_time: str = Field(None, description="支付截止(释放)时间")
    pay_url: str = Field(None, description="支付链接")
    async_pay: bool = Field(..., description="是否异步支付")

    trade_no: Optional[str] = Field(None, description="交易单号")
    pay_account: Optional[str] = Field(None, description="支付账号")
    pay_amount: Optional[float] = Field(None, description="支付金额")

    extra: Optional[Dict[str, Any]] = Field(default_factory=dict, description="扩展字段")

    @field_validator('dep_airport_code', 'arr_airport_code', 'flight_no', 'currency_code', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        return v


class BookResultCallbackRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_order/crawler/callback/book/result'

    task_info: Dict[str, Any] = Field(..., description="任务信息")
    data: Optional[BookResult] = Field(None, description="结果数据")
    error: ErrorInfo = Field(..., description="错误信息")
