from datetime import datetime
from typing import ClassVar, List, Optional, Union

from pydantic import BaseModel, Field
from commons.sdks.base import GLOBAL_MODEL_CONFIG, BaseRequestModel


class OrderInfo(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    ota_code: str = Field(..., description="平台代码")
    airline_code: str = Field(..., description="航空公司代码")
    ota_order_no: str = Field(..., description="平台订单号")
    dep_city_code: str = Field(..., description="出发城市三字码")
    arr_city_code: Optional[str] = Field(None, description="到达城市三字码")
    dep_airport_code: str = Field(..., description="出发机场三字码")
    arr_airport_code: str = Field(..., description="到达机场三字码")
    trip_type: str = Field(..., description="行程类型")
    dep_date: str = Field(..., description="出发日期")
    return_date: Optional[str] = Field(None, description="返回日期")
    adult_num: int = Field(..., description="成人数量")
    child_num: Optional[int] = Field(0, description="儿童数量")
    infant_num: Optional[int] = Field(0, description="婴儿数量")
    fare_key: str = Field(..., description="票价字符串")
    fare_type: str = Field(..., description="票价类型")
    fare_snapshot: Optional[str] = Field(None, description="票价快照")
    flight_nos: str = Field(..., description="航班号")
    total_price: float = Field(..., description="总价")
    expire_time: Optional[Union[str, datetime]] = Field(..., description="订单失效时间")
    src_currency: str = Field(..., description="原币种")
    src_adult_base: float = Field(..., description="成人基准价格")
    src_adult_tax: float = Field(..., description="成人税费")
    mock_pnr: str = Field(..., description="mock PNR")
    pnr: str = Field(..., description="PNR")
    status: Optional[int] = Field(None, description="订单状态")


class CabinInfo(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG
    code: str = Field(..., description="仓位代码")
    cabin_class: str = Field(..., description="舱位等级")
    name: str = Field(..., description="名称")
    desc: str = Field(..., description="详细描述")


# class StopInfo(BaseModel):
#     model_config = GLOBAL_MODEL_CONFIG
#     stop_index: int = Field(..., description="经停记录顺序")
#     stop_airport_code: str = Field(..., description="航段到达机场三字码")
#     stop_airport_name: str = Field(..., description="航段到达机场名称")
#     stop_arr_time: str = Field(..., description="到达时间")
#     stop_dep_time: str = Field(..., description="起飞时间")


# class IncludedDetail(BaseModel):
#     model_config = GLOBAL_MODEL_CONFIG
#     code: str = Field(..., description="ID 或编码")
#     name: str = Field(..., description="名称")
#     desc: str = Field(..., description="描述")
#     price: Union[int, float] = Field(..., description="价格")


class SegmentInfo(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    segment_index: int = Field(..., description="航段序号，用于拼接后排序")

    flight_no: str = Field(..., description="完整航班号，如 TR101")

    dep_airport_code: str = Field(..., description="出发机场代码")

    arr_airport_code: str = Field(..., description="到达机场代码")

    dep_date: str = Field(..., description="航段起飞日期")
    dep_time: str = Field(..., description="航段起飞时间")
    arr_date: str = Field(..., description="航段到达日期")
    arr_time: str = Field(..., description="航段到达时间")
    stop_times: int = Field(..., description="经停次数")
    cabin: CabinInfo = Field(..., description="舱位详情")
    # stops: Optional[List[StopInfo]] = Field(None, description="经停记录")
    # includes: List[IncludedDetail] = Field(..., description="包含")


class TripInfo(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    trip_index: int = Field(..., description="行程序号: 1 标识去程, 2 表示回程")

    segments: List[SegmentInfo] = Field(..., description="航段列表")


class BaggageOrder(BaseModel):
    outer_id: Optional[str] = Field(None, description='行李订单ID')
    baggage_type: Union[int, str] = Field(..., description='行李类型')
    baggage_weight: int = Field(..., description='行李重量')
    baggage_size: int = Field(..., description='行李尺寸')
    baggage_count: int = Field(..., description='行李数量')
    baggage_price: Optional[float] = Field(None, description='行李价格')


class OrderPassengerInfo(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    passenger_type: Optional[str] = Field(None, description="乘客类型")
    passenger_name: Optional[str] = Field(..., description="乘客姓名")
    last_name: Optional[str] = Field(..., description="乘客姓")
    first_name: Optional[str] = Field(..., description="乘客名")
    sex: Optional[str] = Field(None, description=f"乘客性别")
    country_code: Optional[str] = Field(None, description="乘客国籍")
    mobile: Optional[str] = Field(..., description="乘客手机号")
    card_type: Optional[str] = Field(None, description="乘客证件类型")
    card_no: Optional[str] = Field(..., description="乘客证件号")
    card_valid_date: Optional[str] = Field(..., description="乘客证件有效期")
    card_country_code: Optional[str] = Field(None, description="乘客证件签发国家")
    birthday: Optional[str] = Field(..., description="乘客出生日期")
    ticket_no: Optional[str] = Field(None, description="子票号")
    base_price: Optional[float] = Field(None, description="基础票价")
    tax: Optional[float] = Field(None, description="税费")
    total_price: Optional[float] = Field(None, description="总价")
    # add_ons: List[AddOnsList] = Field(None, description="附加服务")
    sub_pnr: Optional[str] = Field(None, description="子PNR")
    baggage_orders: Optional[List[BaggageOrder]] = Field(None, description="行李订单")


class OrderContactInfo(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    contact_name: Optional[str] = Field(None, description="乘客姓名")
    mobile: Optional[str] = Field(None, description="乘客手机号")
    email: Optional[str] = Field(None, description="乘客邮箱")
    country_code: Optional[str] = Field(None, description="联系人国籍")


class FlightOrderPublicCreateOrderRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_order/public/order/create'

    order_info: Optional[OrderInfo] = Field(..., description="订单信息")
    trips: List[TripInfo] = Field(..., description="行程详情")
    passengers: List[OrderPassengerInfo] = Field(..., description="乘客信息")
    contact: OrderContactInfo = Field(..., description="联系人信息")
    # add_ons: FlightAddOns = Field(..., description="附加服务详情")


class FlightOrderPublicFinishOrderRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_order/public/order/finish'

    order_no: str = Field(..., description="订单号")


class FlightOrderPublicCancelOrderRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_order/public/order/cancel'

    order_no: str = Field(..., description="订单号")


class FlightOrderPublicStartTaskRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_order/public/order/task/start'

    order_no: str = Field(..., description="订单号")
    task_type: str = Field(..., description="任务类型")
    unique_id: Optional[str] = Field(None, description="唯一键")
