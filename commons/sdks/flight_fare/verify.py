from typing import Any, ClassVar, Dict, List, Optional, Union
from loguru import logger
from pydantic import BaseModel, Field, field_validator


from commons.sdks.base import BaseRequestModel
from commons.sdks.crawler.create_order import Passenger, PassengerAuxBaggage
from commons.sdks.flight_fare.public import FarePublicGetVerifyOrderRequest


class FlightVerifyRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_fare/public/search/single'

    fare_key: str = Field(..., description="运价key")
    dep_airport_code: str = Field(..., description="出发机场三字码")
    arr_airport_code: str = Field(..., description="到达机场三字码")
    flight_no: str = Field(..., description="航班号")
    adult: Optional[int] = Field(1, description="成人数量")
    child: Optional[int] = Field(0, description="儿童数量")
    infant: Optional[int] = Field(0, description="婴儿数量")
    use_cache: Optional[bool] = Field(True, description="使用缓存数据进行校验")

    @field_validator('dep_airport_code', 'arr_airport_code', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        return v


class FlightRealTimeVerifyRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_fare/public/verify/real_time'

    fare_key: str = Field(..., description="运价key")
    dep_airport_code: str = Field(..., description="出发机场三字码")
    arr_airport_code: str = Field(..., description="到达机场三字码")
    dep_time: Optional[str] = Field(None, description="出发时间")
    flight_no: str = Field(..., description="航班号")
    adult: Optional[int] = Field(1, description="成人数量")
    child: Optional[int] = Field(0, description="儿童数量")
    infant: Optional[int] = Field(0, description="婴儿数量")
    keep_time: int = Field(60, description="验价持续时间")
    mock_pnr: Optional[str] = Field(None, description="假pnr")

    @field_validator('dep_airport_code', 'arr_airport_code', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        return v


class FlightVerifyOrderCreateRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_fare/public/verify/order/create'

    order_no: str = Field(..., description="订单号")
    mock_pnr: str = Field(..., description="假pnr")
    fare_key: str = Field(..., description="运价key")
    dep_airport_code: str = Field(..., description="出发机场三字码")
    arr_airport_code: str = Field(..., description="到达机场三字码")
    flight_no: str = Field(..., description="航班号")
    dep_time: str = Field(..., description="出发时间")
    adult: Optional[int] = Field(1, description="成人数量")
    child: Optional[int] = Field(0, description="儿童数量")
    infant: Optional[int] = Field(0, description="婴儿数量")
    src_adult_base: float = Field(..., description="成人单价")
    src_adult_tax: float = Field(..., description="成人税费")
    passengers: List[Passenger] = Field(..., description="乘客信息")
    session_id: Optional[str] = Field(None, description="session_id")
    passenger_auxes: List[Union[PassengerAuxBaggage]] = Field(default_factory=list, description="乘客附属信息")
    fare_type: Optional[str] = Field(None, description="运价类型")

    @field_validator('dep_airport_code', 'arr_airport_code', 'flight_no', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        return v


class FlightVerifyOrderGetRequest(FarePublicGetVerifyOrderRequest):
    # 废弃
    # 改用 FarePublicGetVerifyOrderRequest
    # 继承 FarePublicGetVerifyOrderRequest 是为了兼容旧的接口
    pass
