from typing import Any, ClassVar, Dict
from loguru import logger
from pydantic import BaseModel, Field, model_validator

from commons.fastapi.schemas.common_schemas import BaseApiOut
from commons.sdks.base import BaseRequestModel


class GetCurrentAdminRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_fare/admin/login/info'


# class ExchangeRateResult(GetCurrentAdminRequest):

#     rate: float = Field(..., description="汇率")


# class GetExchangeRateResponse(BaseApiOut):
#     data: ExchangeRateResult
