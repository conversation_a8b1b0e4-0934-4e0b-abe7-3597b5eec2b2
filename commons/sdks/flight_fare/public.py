from datetime import datetime
from typing import Any, ClassVar, Dict, List, Optional, Union

from pydantic import BaseModel, Field, field_validator
from commons.sdks.base import GLOBAL_MODEL_CONFIG, BaseRequestModel
from commons.sdks.common_model import ErrorInfo


class GetTicketChannelsRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_fare/public/ticket/channels'

    mock_pnr: str = Field(..., description="假pnr")
    dep_airport_code: str = Field(..., description="出发机场三字码")
    arr_airport_code: str = Field(..., description="到达机场三字码")
    flight_no: str = Field(..., description="航班号")
    dep_date: str = Field(..., description="出发日期")
    adult: Optional[int] = Field(1, description="成人数量")
    child: Optional[int] = Field(0, description="儿童数量")
    infant: Optional[int] = Field(0, description="婴儿数量")
    min_adult_base: float = Field(..., description="成人单价")


class GetAirlineAccountRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_fare/public/airline/account/get'

    airline_code: str = Field(..., description="航空公司代码")
    account_name: Optional[str] = Field(None, description="账号名称")

    @field_validator('airline_code', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        if v and isinstance(v, list):
            return [i.upper() for i in v]
        return v


class GetAllScheduleRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_fare/public/schedule/all'

    airline_code: str = Field(None, description="航空公司代码")

    @field_validator('airline_code', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        if v and isinstance(v, list):
            return [i.upper() for i in v]
        return v


class FarePublicGetVerifyOrderRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_fare/public/verify/order/get'

    order_no: Optional[str] = Field(None, description="订单号")
    mock_pnr: str = Field(..., description="假pnr")


class FarePublicFinishVerifyOrderRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_fare/public/verify/order/finish'

    mock_pnr: str = Field(..., description="假pnr")


class FarePublicGetExchangeRateRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_fare/public/exchange/rate/get'

    airline_code: Optional[str] = Field(None, description="航空公司代码")
    src_currency: str = Field(..., description="原币种")
    dst_currency: Optional[str] = Field(default='CNY', description="目标币种")
    src_price: Union[int, float] = Field(..., description="原价")
