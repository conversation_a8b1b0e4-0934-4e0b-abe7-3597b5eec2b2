from typing import Any, ClassVar, Dict, Optional
from loguru import logger
from pydantic import BaseModel, Field, model_validator

from commons.fastapi.schemas.common_schemas import BaseApiOut
from commons.sdks.base import BaseRequestModel


class FlightSearchRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_fare/public/search'

    channel_code: str = Field(..., description="产品渠道编码")
    dep_city_code: str = Field(..., description="出发城市三字码")
    arr_city_code: str = Field(..., description="到达城市三字码")
    dep_date: str = Field(..., description="出发日期")
    adult: Optional[int] = Field(1, description="成人数量")
    child: Optional[int] = Field(0, description="儿童数量")
    infant: Optional[int] = Field(0, description="婴儿数量")
