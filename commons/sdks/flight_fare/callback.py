from datetime import datetime
from typing import Any, ClassVar, Dict, List, Optional, Union

from pydantic import BaseModel, Field, field_validator
from commons.sdks.base import GLOBAL_MODEL_CONFIG, BaseRequestModel
from commons.sdks.common_model import ErrorInfo


class TaskInfo(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    verify_task_id: int = Field(..., description="验价任务 ID")
    unique_id: str = Field(..., description="任务唯一 ID，用于日志合并")
    site_code: str = Field(..., description="站点代码")
    site_type: str = Field(..., description="站点类型")
    dep_airport_code: str = Field(..., description="出发机场代码")
    arr_airport_code: str = Field(..., description="到达机场代码")
    dep_date: str = Field(..., description="出发日期")
    return_date: Optional[str] = Field(None, description="返程日期（往返时必填）")
    trip_type: str = Field(..., description="行程类型: ow (单程), ct (标准联程), rt (往返)")
    status: str = Field(..., description="状态: 待抓取 / 进行中 / 完成 / 失败")
    airline_code: str = Field(..., description="航司二字码")
    schedule_id: int = Field(..., description="时刻表记录 ID")
    fetch_rule_id: int = Field(..., description="抓取规则 ID")
    expire_seconds: int = Field(..., description="结果缓存时长（秒）")
    task_key: str = Field(..., description="任务 Key")
    worker_id: int = Field(0, description="任务所属 Worker ID")
    create_time: Optional[str] = Field('', description="任务创建时间")
    start_time: Optional[str] = Field('', description="任务开始时间")
    end_time: Optional[str] = Field('', description="任务结束时间")
    expire_time: Optional[str] = Field('', description="任务过期时间")

    @field_validator('dep_airport_code', 'arr_airport_code', 'airline_code', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        if v and isinstance(v, list):
            return [i.upper() for i in v]
        return v


class CabinInfo(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    code: str = Field(..., description="舱位代码")
    cabin_class: str = Field(..., description="舱位等级")
    name: str = Field(..., description="名称")
    desc: str = Field(..., description="详细描述")


class StopInfo(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    stop_index: int = Field(..., description="经停记录顺序")
    stop_airport_code: str = Field(..., description="航段到达机场三字码")
    dep_date: str = Field(..., description="起飞日期")
    dep_time: str = Field(..., description="起飞时间")
    arr_date: str = Field(..., description="到达日期")
    arr_time: str = Field(..., description="到达时间")


class BaggageDetail(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    count: int = Field(0, description="数量")
    weight: Optional[Union[int, float]] = Field(None, description="单件重量")
    all_weight: Optional[Union[int, float]] = Field(None, description="总重量")


class MealDetail(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    code: str = Field(..., description="ID 或编码")
    name: str = Field(..., description="名称")
    desc: str = Field(..., description="详细描述")


class AddOnDetail(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    code: str = Field('', description="ID 或编码")
    name: str = Field('', description="名称")
    desc: str = Field('', description="描述")
    price: Union[int, float] = Field(..., description="价格")
    weight: Optional[Union[int, float]] = Field(None, description="重量")
    # markup_price: Optional[Union[int, float]] = Field(None, description="经过加价后的价格，用于报价接口返回")


class FlightSegment(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    is_journey: Optional[bool] = Field(None, description="是否为弃程数据，用于报价接口返回")
    segment_index: int = Field(..., description="航段序号，用于拼接后排序")
    flight_number: str = Field(..., description="航班号")
    flight_no: str = Field(..., description="完整航班号，如 TR101")
    dep_airport_code: str = Field(..., description="航段起飞机场三字码")
    arr_airport_code: str = Field(..., description="航段到达机场三字码")
    dep_date: str = Field(..., description="航段起飞日期")
    dep_time: str = Field(..., description="航段起飞时间")
    arr_date: str = Field(..., description="航段到达日期")
    arr_time: str = Field(..., description="航段到达时间")
    stop_times: int = Field(..., description="经停次数")
    cabin: CabinInfo = Field(..., description="舱位详情")
    stops: Optional[List[StopInfo]] = Field(None, description="经停记录")
    aircraft_code: str = Field(None, description="飞机机型")
    share_code: bool = Field(None, description="是否共享航班")

    @field_validator('dep_airport_code', 'arr_airport_code', 'flight_number', 'flight_no', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        if v and isinstance(v, list):
            return [i.upper() for i in v]
        return v


class TripFare(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    base: Union[int, float] = Field(..., description="基础价，原币种")
    markup_price: Optional[Union[int, float]] = Field(None, description="经过加价的基础价，用于报价接口返回")
    tax: Union[int, float] = Field(..., description="税费")
    total: Union[int, float] = Field(..., description="含税总价")
    quantity: Union[int, float] = Field(..., description="库存数量")


class BaggageInfo(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    hand: List[AddOnDetail] = Field(None, description="手提行李")
    cabin: List[AddOnDetail] = Field(None, description="托运行李")


class FlightAddOns(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    baggage: BaggageInfo = Field(None, description="额外行李选项")
    meals: List[AddOnDetail] = Field(None, description="餐食选项")
    seat: List[AddOnDetail] = Field(None, description="座位选择")
    board_first: List[AddOnDetail] = Field(None, description="优先登机选项")
    flex: List[AddOnDetail] = Field(None, description="灵活改签选项")
    wifi: List[AddOnDetail] = Field(None, description="Wi-Fi 选项")


class FlightTrip(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    trip_index: int = Field(..., description="行程序号: 1 标识去程, 2 表示回程")
    airline_codes: List[str] = Field(..., description="航司二字码列表")
    cabin_codes: List[str] = Field(..., description="舱位代码列表")
    flight_nos: List[str] = Field(..., description="完整航班号")
    dep_airport_code: str = Field(..., description="出发机场代码")
    arr_airport_code: str = Field(..., description="到达机场代码")
    dep_date: str = Field(..., description="起飞日期")
    dep_time: str = Field(..., description="起飞时间")
    arr_date: str = Field(..., description="到达日期")
    arr_time: str = Field(..., description="到达时间")
    stop_times: int = Field(..., description="经停次数")
    across_days: int = Field(..., description="跨越的天数")
    segments: List[FlightSegment] = Field(..., description="航段列表")
    fares: Dict[str, TripFare] = Field(..., description="乘客类型的票价详情")
    includes: Dict[str, Optional[Union[Dict[str, Union[BaggageDetail, List[BaggageDetail]]], MealDetail, bool]]] = (
        Field(..., description="包含的服务")
    )
    add_ons: FlightAddOns = Field(default_factory=dict, description="附加服务详情")

    @field_validator(
        'dep_airport_code', 'arr_airport_code', 'airline_codes', 'cabin_codes', 'flight_nos', mode='before'
    )
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        if v and isinstance(v, list):
            return [i.upper() for i in v]
        return v


class ExchangeRates(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    currency_code: str = Field(..., description="官网结算币种三字码")
    rates: Dict[str, str] = Field(default_factory=dict, description="汇率")
    rate: Dict[str, Any] = Field(default_factory=dict, description="汇率")

    @field_validator('currency_code', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        return v


class ResultItem(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    trip_type: str = Field(..., description="行程类型: ow (单程), ct (标准联程), rt (往返)")
    origin_trip_type: Optional[str] = Field(None, description="原始行程类型，用于报价接口返回")
    trips: List[FlightTrip] = Field(..., description="行程详情")


class SearchData(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG

    results: List[ResultItem] = Field(default_factory=list, description="行程详情")
    exchange: ExchangeRates = Field(None, description="汇率信息")


class VerifyCallbackRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_fare/crawler/callback/verify/result'

    task_info: TaskInfo = Field(..., description="任务信息")
    data: Optional[SearchData] = Field(None, description="结果数据")
    error: ErrorInfo = Field(..., description="错误信息")


class HoodCallbackRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/flight_fare/crawler/callback/hood/result'

    seat_num: int = Field(..., description="占座数量")
    currency_code: str = Field(..., description="币种")
    dep_airport_code: str = Field(..., description="出发机场代码")
    arr_airport_code: str = Field(..., description="到达机场代码")
    dep_date: str = Field(..., description="出发日期")
    flight_no: str = Field(..., description="航班号")
    expired_time: str = Field(..., description="过期时间")
    flight_info: ResultItem = Field(..., description="航班信息")
    extra: dict = Field(default_factory=dict, description="额外信息")
