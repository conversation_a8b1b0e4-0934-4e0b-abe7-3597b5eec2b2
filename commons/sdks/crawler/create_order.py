from typing import Any, ClassVar, Dict, List, Optional, Union
from pydantic import BaseModel, Field, field_validator
from pydantic.main import IncEx
from commons.sdks.base import BaseRequestModel


class PassengerAuxBaggage(BaseModel):
    name: str = Field(..., description="姓名")
    dep_airport_code: str = Field(..., description="出发机场三字码")
    arr_airport_code: str = Field(..., description="到达机场三字码")
    dep_date: str = Field(..., description="出发日期")
    flight_no: str = Field(..., description="航班号")
    dep_time: str = Field(..., description="出发时间")
    outer_id: str = Field(..., description="外部ID")
    aux_type: str = Field('baggage', description="附属类型")
    weight: float = Field(..., description="重量")
    size: float = Field(None, description="尺寸")
    count: int = Field(..., description="数量")
    price: float = Field(None, description="行李价格")
    desc: str = Field(None, description="描述")


class Passenger(BaseModel):
    name: str = Field(None, description="姓名")
    last_name: str = Field(..., description="姓")
    first_name: str = Field(..., description="名")
    birthday: str = Field(..., description="出生日期")
    sex: str = Field(..., description="性别")
    passenger_type: str = Field(..., description="乘客类型")
    country: str = Field(..., description="国籍")
    card_no: str = Field(..., description="证件号码")
    card_valid_date: str = Field(..., description="证件有效期")
    card_country: str = Field(..., description="证件国家")
    baggages: List[PassengerAuxBaggage] = Field(default_factory=list, description="行李信息")

    @field_validator('country', 'card_country', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        return v


class Contact(BaseModel):
    name: str = Field(..., description="姓名")
    last_name: str = Field(..., description="姓")
    first_name: str = Field(..., description="名")
    mobile: str = Field(..., description="手机号")
    email: str = Field(..., description="邮箱")
    country: str = Field(..., description="国籍")
    area_code: str = Field(..., description="区号")
    city: str = Field(..., description="城市")
    post_code: str = Field(..., description="邮编")
    address: str = Field(..., description="地址")


class CreateOrderRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/crawler/{airline_code}/public/order/create'

    callback_url: str = Field(..., description="回调 URL")
    start_task_url: Optional[str] = Field(None, description="启动任务 URL")
    task_type: Optional[str] = Field(None, description="任务类型")
    order_no: str = Field(..., description="订单号")
    mock_pnr: str = Field(..., description="模拟PNR")

    airline_code: str = Field(..., description="航司二字码")
    dep_airport_code: str = Field(..., description="出发机场三字码")
    arr_airport_code: str = Field(..., description="到达机场三字码")
    dep_date: str = Field(..., description="出发日期")
    flight_no: str = Field(..., description="航班号")
    adult: Optional[int] = Field(1, description="成人数量")
    child: Optional[int] = Field(0, description="儿童数量")
    infant: Optional[int] = Field(0, description="婴儿数量")
    currency_code: Optional[str] = Field(None, description="币种三字码")
    src_adult_base: float = Field(..., description="成人单价")
    src_adult_tax: float = Field(..., description="成人税费")
    passengers: List[Passenger] = Field(..., description="乘客信息")
    contact: Contact = Field(default_factory=dict, description="联系人信息")
    base_float: Optional[int] = Field(0, description="损失浮动")
    auto_try_times: Optional[int] = Field(1, description="自动尝试次数")
    # 平台订单给出的起飞时间
    dep_time: Optional[str] = Field(None, description="出发时间")
    # 无需与客人核实的最大延误时间
    dep_diff_minutes: Optional[int] = Field(None, description="出发时间差分钟")
    # passenger_auxes: List[Union[PassengerAuxBaggage]] = Field(default_factory=list, description="乘客附属信息")

    @field_validator(
        'dep_airport_code', 'arr_airport_code', 'airline_code', 'flight_no', 'currency_code', mode='before'
    )
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        return v


class ConfirmPayRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/crawler/{airline_code}/public/order/confirm_pay'

    callback_url: Optional[str] = Field(None, description="回调 URL")

    airline_code: str = Field(..., description="航司二字码")
    order_no: str = Field(..., description="订单号")
    real_pnr: str = Field(..., description="真实PNR")
    currency_code: str = Field(..., description="币种三字码")
    total_price: float = Field(..., description="总价")
    account_name: str = Field(..., description="账号名称")
