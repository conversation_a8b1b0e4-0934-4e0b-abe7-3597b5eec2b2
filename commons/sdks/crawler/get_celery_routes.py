from typing import Any, ClassVar, Dict, List, Optional
from pydantic import BaseModel, Field, field_validator
from pydantic.main import IncEx
from commons.sdks.base import BaseRequestModel


class GetCeleryRoutesRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/crawler/{crawler_code}/public/celery/routes'
    # request_exclude: ClassVar[IncEx] = set(['crawler_code'])

    crawler_code: str = Field(..., description="爬虫模块标识码，用小写的航司二字码")
