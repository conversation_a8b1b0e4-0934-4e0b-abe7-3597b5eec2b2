from typing import Any, ClassVar, Dict, List, Optional
from pydantic import BaseModel, Field, field_validator
from pydantic.main import IncEx
from commons.sdks.base import BaseRequestModel


class CreateHoodRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/crawler/{crawler_code}/public/hood/create'
    # request_exclude: ClassVar[IncEx] = set(['crawler_code'])

    crawler_code: str = Field(..., description="爬虫模块标识码，用小写的航司二字码")

    callback_url: str = Field(None, description="回调 URL")

    po_rule_code: str = Field(..., description="预订规则")
    dep_airport_code: str = Field(..., description="出发机场")
    arr_airport_code: str = Field(..., description="到达机场")
    dep_date: str = Field(..., description="出发日期")
    flight_no: str = Field(..., description="航班号")
    currency_code: str = Field(None, description="币种")

    @field_validator('crawler_code', mode='before')
    @classmethod
    def to_lower(cls, v: Any):
        if isinstance(v, str):
            return v.lower()
        return v

    @field_validator('dep_airport_code', 'arr_airport_code', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        return v
