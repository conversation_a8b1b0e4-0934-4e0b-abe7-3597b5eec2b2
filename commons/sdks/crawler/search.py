from typing import Any, ClassVar, Dict, List, Optional
from pydantic import BaseModel, Field, field_validator
from pydantic.main import IncEx
from commons.sdks.base import BaseRequestModel


class SearchRequest(BaseRequestModel):
    endpoint: ClassVar[str] = '/api/v1/crawler/{airline_code}/public/search'

    airline_code: str = Field(..., description="航司二字码")
    dep_airport_code: str = Field(..., description="出发机场三字码")
    arr_airport_code: str = Field(..., description="到达机场三字码")
    dep_date: str = Field(..., description="出发日期")
    adult: Optional[int] = Field(1, description="成人数量")
    child: Optional[int] = Field(0, description="儿童数量")
    infant: Optional[int] = Field(0, description="婴儿数量")
    currency_code: Optional[str] = Field(None, description="币种三字码")

    # @field_validator('crawler_code', mode='before')
    # @classmethod
    # def to_lower(cls, v: Any):
    #     if isinstance(v, str):
    #         return v.lower()
    #     return v

    @field_validator('dep_airport_code', 'arr_airport_code', 'airline_code', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        return v
