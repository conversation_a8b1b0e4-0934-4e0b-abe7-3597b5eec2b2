from . import base, flight_fare, flight_order, flight_pre_order, pay_center, crawler
import importlib
import pkgutil
from typing import Dict, Type, List


_SUB_SDK_CLASSES: Dict[str, Type[base.SdkClient]] = {}


def _find_request_classes(module_name: str) -> List[Type[base.BaseRequestModel]]:
    """递归查找子模块中所有Request类"""
    request_classes = []
    base_module = importlib.import_module(f'.{module_name}', __package__)

    # 遍历模块所有子模块
    for _, submod_name, is_pkg in pkgutil.walk_packages(base_module.__path__, prefix=f'{base_module.__name__}.'):
        if is_pkg or '__init__' in submod_name:
            continue

        try:
            submod = importlib.import_module(submod_name)
            for attr in dir(submod):
                cls = getattr(submod, attr)
                if isinstance(cls, type) and issubclass(cls, base.BaseRequestModel) and cls != base.BaseRequestModel:
                    request_classes.append(cls)
        except Exception:
            continue

    return request_classes


def _generate_client_class(module_name: str):
    class SubSdkClient(base.SdkClient):
        _module_name = module_name

        def __init__(self, host: str):
            super().__init__(host=host)
            self._bind_interface_methods()

        def _bind_interface_methods(self):
            """动态绑定所有Request类的方法"""
            request_classes = _find_request_classes(self._module_name)

            for request_class in request_classes:
                # 从类名生成方法名：GetTicketChannelsRequest -> get_ticket_channels
                method_base = request_class.__name__[:-7]  # 去掉Request后缀
                method_name = (
                    ''.join(['_' + c.lower() if c.isupper() else c for c in method_base]).lstrip('_').replace('__', '_')
                )

                # 创建方法对
                self._create_method_pair(method_name, request_class)

        def _create_method_pair(self, method_name: str, request_class: Type[base.BaseRequestModel]):
            # 同步方法
            def sync_method(self, **kwargs):
                req = request_class(**kwargs)
                if 'task_info' in kwargs and 'callback_url' in kwargs['task_info']:
                    req.endpoint = kwargs['task_info']['callback_url']
                return self.send(req)

            # 异步方法
            async def async_method(self, **kwargs):
                req = request_class(**kwargs)
                if 'task_info' in kwargs and 'callback_url' in kwargs['task_info']:
                    req.endpoint = kwargs['task_info']['callback_url']
                return await self.send_async(req)

            # 绑定到实例
            setattr(self, method_name, sync_method.__get__(self))
            setattr(self, f"{method_name}_async", async_method.__get__(self))

    return SubSdkClient


def get_sub_sdk(module_name: str, host: str) -> base.SdkClient:
    if module_name not in _SUB_SDK_CLASSES:
        client_class = _generate_client_class(module_name)
        _SUB_SDK_CLASSES[module_name] = client_class

    return _SUB_SDK_CLASSES[module_name](host=host)
