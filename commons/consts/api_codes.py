from enum import unique
from typing import Optional, Union
from commons.cores.base_const import BaseEnum

# 2024-11-20
# 这里需要对错误重新定义


@unique
class ApiCodes(BaseEnum):
    """接口返回码"""

    SUCCESS = (0, '成功')
    # 通用错误 1-999，9999
    UNKNOWN = (9999, '未知错误')
    NO_FOUND = (404, '记录未找到')
    PARAMS_ERROR = (422, '参数错误')

    LOCK_ERROR = (600, '已被锁定')
    DUPLICATE_ERROR = (601, '重复记录')
    TIMEOUT_ERROR = (602, '请求超时')
    TASK_RUNNING = (603, '任务运行中')

    # SITE_ERROR = (9002, '站点报错')
    # NO_SEARCH_RESULT = (9003, '无搜索结果')
    # SEARCH_TIMEOUT = (9004, '限定时间内无搜索结果')
    # NO_ACCOUNT = (9005, '无可用账号')
    # NO_FOUND = (9006, '记录未找到')
    # LOCK_ERROR = (9007, '已被锁定')
    # DUPLICATE_ERROR = (9008, '重复记录')

    # 运价错误 1000-1999
    FARE_VERIFY_INVALID = (1001, '运价已失效或不存在')
    FARE_VERIFY_TICKET_NOT_ENOUGH = (1002, '票数不足')
    FARE_VERIFY_NO_RESULT = (1003, '无运价结果')
    FARE_VERIFY_TAX_ERROR = (1004, '税费错误')
    FARE_VERIFY_REFUND_RULE_ERROR = (1005, '退票规则错误')
    FARE_VERIFY_SESSION_ERROR = (1006, '验价会话已失效')
    FARE_VERIFY_CHANGE_ERROR = (1007, '变价错误')
    FARE_VERIFY_PAY_TIMEOUT = (1008, '支付超时')
    FARE_VERIFY_STOP_BOOK = (1009, '停止预订')
    FARE_SCAN_NO_BOOK_RESULT = (1010, '扫描出票无预占座结果')

    # 订单错误 2000-2999
    ORDER_EXPIRE = (2001, '订单过期')
    ORDER_COMPARE_ERROR = (2002, '订单信息错误')
    ORDER_PUSH_ERROR = (2003, '订单推送失败')
    ORDER_STATUS_ERROR = (2004, '订单状态错误')
    ORDER_PAY_LIMIT_TIME = (2005, '超过支付限制时间')
    # ORDER_PAY_ERROR = (2006, '订单支付失败')
    ORDER_CHECK_ERROR = (2007, '订单结果校验失败')
    ORDER_PAY_PUSH_ERROR = (2008, '订单支付推送失败')
    ORDER_CHECK_PUSH_ERROR = (2009, '订单结果推送失败')
    ORDER_PNR_ERROR = (2010, '订单PNR错误')
    ORDER_PNR_EXPIRE_TIME_ERROR = (2011, '订单PNR过期时间错误')
    ORDER_INIT_FAIL = (2012, '订单初始化失败')
    ORDER_AUTO_BOOK_ERROR = (2013, '目标航司不支持自动订座')

    # 压位系统错误 3000-3999
    # PRE_ORDER_PUSH_ERR = (3001, '压位系统推送预订失败')
    PRE_ORDER_FARE_EXISTS = (3002, '压位运价已存在')

    # 爬虫错误 4000-4999
    BOOK_ERROR = (4001, '占座失败')
    BOOK_PAY_ERROR = (4002, '占支付失败')
    HOOD_ERROR = (4003, '压位占座失败')

    BOOK_SEARCH_FAIL = (4004, '搜索失败')
    BOOK_TICKET_NOT_ENOUGH = (4005, '票数不足')
    BOOK_SEARCH_TIMEOUT = (4006, '限定时间内无搜索结果')
    BOOK_PRICE_ERROR = (4007, '航班价格升高，无法预订')
    BOOK_FLIGHT_NOT_FOUND = (4008, '航班已售空')
    BOOK_SCAN_NO_LOWER = (4009, '暂无更低价格')
    BOOK_FORBIDDEN = (4010, '触发禁止访问')

    HOOD_NO_RESULT = (4011, '无可压位的价格')
    # NOT_SUPPORT_AUTO_PAY = (4002, '目标航司不支持自动支付')
    # NOT_SUPPORT_AUTO_CHECK = (4003, '目标航司不支持订座结果自动检测')

    BOOK_BAGGAGE_ERROR = (4012, '行李选项错误')
    BOOK_DEP_TIME_ERROR = (4013, '出发时间错误，需客人确认是否继续出票')
    BOOK_PAID_ERROR = (4014, '订单已支付')

    def generate_api_result(
        self,
        msg: str = None,
        ext_msg: str = None,
        data: Optional[Union[dict, str]] = None,
        client_ip: str = '',
        request_id: str = '',
        status: str = 'success',
    ) -> dict:
        code = self._value_
        if status == 'success' and code != ApiCodes.SUCCESS.value:
            status = 'fail'

        # 传入msg时会覆盖label和ext_msg
        if not msg:
            msg = self.label

        return BaseEnum.generate_result(
            code=code, msg=msg, ext_msg=ext_msg, data=data, client_ip=client_ip, request_id=request_id, status=status
        )
