# core/config.py
from dynaconf import Dynaconf
from typing import Any, Dict, List, Optional, Union
import os
import threading
from pathlib import Path

from loguru import logger


class ConfigManager:
    _instance_lock = threading.Lock()
    _settings: Dynaconf = None
    _instance = None

    def __new__(
        cls,
        settings_files: Optional[Union[str, List[str]]] = None,
        settings_data: Optional[Dict] = None,
        reload: bool = False,
        envvar_prefix: str = "APP",
        multiple_envs: bool = False,
    ):
        if not cls._instance:
            with cls._instance_lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
                    cls._initialize(
                        settings_files=settings_files,
                        settings_data=settings_data,
                        reload=reload,
                        envvar_prefix=envvar_prefix,
                        multiple_envs=multiple_envs,
                    )
        return cls._instance

    @classmethod
    def _initialize(
        cls,
        settings_files: Optional[Union[str, List[str]]] = None,
        settings_data: Optional[Dict] = None,
        reload: bool = False,
        envvar_prefix: str = "APP",
        multiple_envs: bool = False,
    ):
        """初始化配置管理器

        Args:
            settings_files: 配置文件路径列表或单个文件路径，支持.toml/.json/.yaml/.yml
            settings_data: 直接传入的配置数据字典
        """
        config_files = []

        # 处理配置文件
        if settings_files:
            if isinstance(settings_files, str):
                settings_files = [settings_files]

            for file in settings_files:
                # 检查文件是否存在
                if os.path.exists(file):
                    ext = Path(file).suffix.lower()
                    if ext in ['.toml', '.json', '.yaml', '.yml']:
                        config_files.append(file)
                        logger.info(f"加载配置文件: {file}，存在相同配置时，以最后加载的配置为准")
                    else:
                        raise ValueError(f"Unsupported config file format: {ext}")
                else:
                    if multiple_envs:
                        logger.warning(f"忽略不存在的配置文件: {file}")
                    else:
                        raise ValueError(f"配置文件不存在: {file}")

        # 如果没有提供配置文件，使用默认配置文件
        if not config_files and not settings_data:
            default_config = "config.toml"
            if os.path.exists(default_config):
                config_files.append(default_config)

        # 初始化Dynaconf
        settings_kwargs = {"settings_files": config_files, "reload": reload, "envvar_prefix": envvar_prefix}

        # 如果提供了直接的配置数据
        if settings_data:
            settings_kwargs["preload"] = [settings_data]

        cls._settings = Dynaconf(**settings_kwargs)

    @classmethod
    def get(cls, key: str, default: Optional[Any] = None) -> Any:
        """获取配置值"""
        return cls._settings.get(key, default)

    @classmethod
    def reload(cls):
        """重新加载配置"""
        if cls._settings:
            cls._settings.reload()

    @classmethod
    def reset(cls):
        """重置配置管理器实例（主要用于测试）"""
        with cls._instance_lock:
            cls._instance = None
            cls._settings = None

    @classmethod
    def update(cls, settings_data: Dict):
        """更新配置数据

        Args:
            settings_data: 要更新的配置数据字典
        """
        if cls._settings:
            cls._settings.update(settings_data)

    @classmethod
    def to_dict(cls):
        return cls._settings.to_dict()

    def __str__(self):
        return str(self._settings)

    def __repr__(self):
        return str(self._settings)
